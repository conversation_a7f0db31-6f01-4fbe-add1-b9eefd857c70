import React, { useState, useEffect } from 'react'
import { globalPriceService, PriceDataState } from '../../services/globalPriceService'
import { binanceWebSocket } from '../../services/binanceWebSocket'

// 🔧 价格数据流测试组件
export function PriceDataTest() {
  const [priceData, setPriceData] = useState<PriceDataState | null>(null)
  const [rawData, setRawData] = useState<any[]>([])
  const [connectionStatus, setConnectionStatus] = useState({
    globalService: false,
    webSocketService: false,
    subscriberCount: 0
  })

  useEffect(() => {
    console.log('🔧 [TEST] 启动价格数据流测试')
    
    // 强制设置localStorage
    localStorage.setItem('trading_status', 'active')
    localStorage.setItem('selected_symbol', 'BTCUSDT')
    
    // 订阅全局价格服务
    const unsubscribe = globalPriceService.subscribe((state: PriceDataState) => {
      console.log('🔍 [TEST] 收到价格数据更新:', state)
      setPriceData(state)
      
      // 记录原始数据
      setRawData(prev => {
        const newData = {
          timestamp: new Date().toLocaleTimeString(),
          price: state.currentPrice,
          bid: state.bid,
          ask: state.ask,
          isConnected: state.isConnected,
          lastUpdate: state.lastUpdate
        }
        return [newData, ...prev.slice(0, 9)] // 保留最近10条记录
      })
    })

    // 监控连接状态
    const statusInterval = setInterval(() => {
      setConnectionStatus({
        globalService: globalPriceService.isConnected(),
        webSocketService: binanceWebSocket.getConnectionStatus(),
        subscriberCount: (globalPriceService as any).subscribers?.length || 0
      })
    }, 1000)

    // 强制重连
    setTimeout(() => {
      console.log('🔄 [TEST] 强制重连全局价格服务')
      globalPriceService.reconnect()
    }, 1000)

    return () => {
      unsubscribe()
      clearInterval(statusInterval)
    }
  }, [])

  const handleForceReconnect = () => {
    console.log('🔄 [TEST] 手动强制重连')
    globalPriceService.reconnect()
  }

  const handleForceUpdate = () => {
    console.log('🔧 [TEST] 强制更新价格数据')
    const testPrice = 100000 + Math.random() * 1000
    const testBid = testPrice - 5
    const testAsk = testPrice + 5

    // 使用调试方法强制更新
    ;(globalPriceService as any).forceUpdatePriceData(testPrice, testBid, testAsk)
  }

  const handleTestWebSocket = () => {
    console.log('🧪 [TEST] 直接测试WebSocket连接')
    
    // 直接测试WebSocket
    const testWs = new WebSocket('wss://fstream.binance.com/ws/btcusdt@ticker')
    
    testWs.onopen = () => {
      console.log('✅ [TEST] 直接WebSocket连接成功')
    }
    
    testWs.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        console.log('📨 [TEST] 直接WebSocket收到数据:', data)
      } catch (error) {
        console.error('❌ [TEST] 数据解析失败:', error)
      }
    }
    
    testWs.onerror = (error) => {
      console.error('❌ [TEST] 直接WebSocket连接失败:', error)
    }
    
    // 10秒后关闭测试连接
    setTimeout(() => {
      testWs.close()
      console.log('🔚 [TEST] 关闭测试WebSocket连接')
    }, 10000)
  }

  return (
    <div className="space-y-6 p-6 bg-slate-900 text-white">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">🔧 价格数据流测试</h2>
        <div className="flex space-x-2">
          <button
            onClick={handleForceReconnect}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            🔄 强制重连
          </button>
          <button
            onClick={handleForceUpdate}
            className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
          >
            🔧 强制更新
          </button>
          <button
            onClick={handleTestWebSocket}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            🧪 测试WebSocket
          </button>
        </div>
      </div>

      {/* 连接状态 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-slate-800 p-4 rounded-lg">
          <h3 className="font-semibold mb-2">🌐 全局价格服务</h3>
          <div className="space-y-1 text-sm">
            <div className="flex justify-between">
              <span>连接状态:</span>
              <span className={connectionStatus.globalService ? 'text-green-400' : 'text-red-400'}>
                {connectionStatus.globalService ? '✅ 已连接' : '❌ 断开'}
              </span>
            </div>
            <div className="flex justify-between">
              <span>订阅者数量:</span>
              <span>{connectionStatus.subscriberCount}</span>
            </div>
            <div className="flex justify-between">
              <span>当前币种:</span>
              <span>{globalPriceService.getCurrentSymbol() || '无'}</span>
            </div>
          </div>
        </div>

        <div className="bg-slate-800 p-4 rounded-lg">
          <h3 className="font-semibold mb-2">🔗 WebSocket服务</h3>
          <div className="space-y-1 text-sm">
            <div className="flex justify-between">
              <span>连接状态:</span>
              <span className={connectionStatus.webSocketService ? 'text-green-400' : 'text-red-400'}>
                {connectionStatus.webSocketService ? '✅ 已连接' : '❌ 断开'}
              </span>
            </div>
            <div className="flex justify-between">
              <span>活跃币种:</span>
              <span>{binanceWebSocket.getActiveSymbol() || '无'}</span>
            </div>
          </div>
        </div>

        <div className="bg-slate-800 p-4 rounded-lg">
          <h3 className="font-semibold mb-2">💾 本地存储</h3>
          <div className="space-y-1 text-sm">
            <div className="flex justify-between">
              <span>交易状态:</span>
              <span>{localStorage.getItem('trading_status') || '未设置'}</span>
            </div>
            <div className="flex justify-between">
              <span>选择币种:</span>
              <span>{localStorage.getItem('selected_symbol') || '未设置'}</span>
            </div>
          </div>
        </div>
      </div>

      {/* 当前价格数据 */}
      <div className="bg-slate-800 p-6 rounded-lg">
        <h3 className="font-semibold mb-4">📊 当前价格数据</h3>
        {priceData ? (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400">
                ${priceData.currentPrice.toFixed(2)}
              </div>
              <div className="text-sm text-slate-400">当前价格</div>
            </div>
            <div className="text-center">
              <div className="text-xl font-bold text-blue-400">
                ${priceData.bid.toFixed(2)}
              </div>
              <div className="text-sm text-slate-400">买入价</div>
            </div>
            <div className="text-center">
              <div className="text-xl font-bold text-red-400">
                ${priceData.ask.toFixed(2)}
              </div>
              <div className="text-sm text-slate-400">卖出价</div>
            </div>
            <div className="text-center">
              <div className={`text-xl font-bold ${priceData.change24h >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                {priceData.change24h >= 0 ? '+' : ''}{priceData.change24h.toFixed(2)}%
              </div>
              <div className="text-sm text-slate-400">24h变化</div>
            </div>
          </div>
        ) : (
          <div className="text-center text-slate-400">
            等待价格数据...
          </div>
        )}
      </div>

      {/* 数据历史 */}
      <div className="bg-slate-800 p-6 rounded-lg">
        <h3 className="font-semibold mb-4">📈 数据更新历史</h3>
        <div className="space-y-2 max-h-60 overflow-y-auto">
          {rawData.length > 0 ? (
            rawData.map((data, index) => (
              <div key={index} className="flex justify-between items-center text-sm border-b border-slate-700 pb-2">
                <span className="text-slate-400">{data.timestamp}</span>
                <span className="text-white">${data.price.toFixed(2)}</span>
                <span className="text-blue-400">${data.bid.toFixed(2)}</span>
                <span className="text-red-400">${data.ask.toFixed(2)}</span>
                <span className={data.isConnected ? 'text-green-400' : 'text-red-400'}>
                  {data.isConnected ? '✅' : '❌'}
                </span>
              </div>
            ))
          ) : (
            <div className="text-center text-slate-400">
              暂无数据更新记录
            </div>
          )}
        </div>
      </div>

      {/* 调试信息 */}
      <div className="bg-slate-800 p-6 rounded-lg">
        <h3 className="font-semibold mb-4">🔍 调试信息</h3>
        <pre className="text-xs text-slate-300 overflow-auto max-h-40">
          {JSON.stringify({
            priceData,
            connectionStatus,
            localStorage: {
              trading_status: localStorage.getItem('trading_status'),
              selected_symbol: localStorage.getItem('selected_symbol')
            },
            globalServiceMethods: {
              isConnected: globalPriceService.isConnected(),
              getCurrentPrice: globalPriceService.getCurrentPrice(),
              getCurrentSymbol: globalPriceService.getCurrentSymbol(),
              getSpread: globalPriceService.getSpread()
            }
          }, null, 2)}
        </pre>
      </div>
    </div>
  )
}
