// 🔧 策略启动诊断脚本
console.log('🚀 启动策略启动诊断工具...')

// 诊断策略启动失败问题
async function diagnoseStartupFailure() {
  console.log('\n📊 ===== 策略启动诊断报告 =====')
  
  // 1. 检查基础环境
  console.log('\n🔍 1. 基础环境检查:')
  
  // 检查必要的服务是否存在
  const services = {
    strategyStateService: typeof strategyStateService !== 'undefined',
    strategyExecutor: typeof strategyExecutor !== 'undefined',
    binanceApi: typeof binanceApi !== 'undefined',
    globalPriceService: typeof globalPriceService !== 'undefined'
  }
  
  console.table(services)
  
  // 2. 检查数据库状态
  console.log('\n💾 2. 数据库状态检查:')
  try {
    if (typeof strategyStateService !== 'undefined') {
      const activeStrategy = await strategyStateService.getActiveStrategy()
      console.log('当前活跃策略:', activeStrategy)
      
      const hasActiveStrategy = strategyStateService.hasActiveStrategy()
      console.log('是否有活跃策略:', hasActiveStrategy)
      
      const strategyStatus = strategyStateService.getStrategyStatus()
      console.log('策略状态:', strategyStatus)
    } else {
      console.error('❌ strategyStateService 未定义')
    }
  } catch (error) {
    console.error('❌ 数据库检查失败:', error)
  }
  
  // 3. 检查API配置状态  
  console.log('\n🔑 3. API配置检查:')
  try {
    // 检查localStorage API配置
    const localStorageConfig = localStorage.getItem('binance_api_config')
    console.log('localStorage API配置:', localStorageConfig ? '✅ 存在' : '❌ 不存在')
    
    // 检查数据库API配置
    if (typeof strategyStateService !== 'undefined') {
      const dbConfig = await strategyStateService.getApiConfig()
      console.log('数据库API配置:', dbConfig ? '✅ 存在' : '❌ 不存在')
      
      if (dbConfig) {
        console.log('数据库API配置详情:', {
          创建时间: new Date(dbConfig.createdAt).toLocaleString(),
          测试网模式: dbConfig.testnet,
          使用代理: dbConfig.useProxy,
          代理地址: dbConfig.useProxy ? `${dbConfig.proxyHost}:${dbConfig.proxyPort}` : '未配置'
        })
        
        // 🌐 特别检查Clash代理配置
        if (dbConfig.useProxy) {
          const isClashProxy = dbConfig.proxyHost === '127.0.0.1' && dbConfig.proxyPort === '7890'
          console.log(isClashProxy ? '✅ 标准Clash代理配置' : '⚠️ 非标准Clash代理配置')
        }
      }
    }
  } catch (error) {
    console.error('❌ API配置检查失败:', error)
  }
  
  // 4. 检查API连接状态
  console.log('\n📡 4. API连接状态检查:')
  try {
    if (typeof binanceApi !== 'undefined') {
      const connectionStatus = binanceApi.getConnectionStatus()
      console.log('API连接状态:', connectionStatus)
      
      // 🌐 检查API当前代理配置
      const apiConfig = binanceApi.getApiConfig()
      console.log('binanceApi配置详情:', {
        '有API密钥': apiConfig.hasApiKey,
        '有API秘钥': apiConfig.hasSecret,
        '连接状态': apiConfig.isConnected,
        '代理配置': apiConfig.proxyConfig
      })
      
      if (apiConfig.proxyConfig?.enabled) {
        const isClashProxy = apiConfig.proxyConfig.host === '127.0.0.1' && apiConfig.proxyConfig.port === '7890'
        console.log(isClashProxy ? '✅ binanceApi使用标准Clash代理' : '⚠️ binanceApi使用非标准代理')
      } else {
        console.log('ℹ️ binanceApi当前使用直连模式')
      }
      
      if (connectionStatus) {
        const metrics = await binanceApi.getLiveTradingMetrics()
        console.log('实时交易指标:', metrics)
      }
    } else {
      console.error('❌ binanceApi 未定义')
    }
  } catch (error) {
    console.error('❌ API状态检查失败:', error)
  }
  
  // 5. 检查localStorage状态
  console.log('\n🗃️ 5. localStorage状态检查:')
  const localStorageData = {
    trading_status: localStorage.getItem('trading_status'),
    selected_symbol: localStorage.getItem('selected_symbol'),
    binance_api_config: !!localStorage.getItem('binance_api_config'),
    strategy_config: !!localStorage.getItem('strategy_config')
  }
  console.table(localStorageData)
  
  // 6. 检查策略执行器状态
  console.log('\n⚡ 6. 策略执行器状态检查:')
  try {
    if (typeof strategyExecutor !== 'undefined') {
      const isRunning = strategyExecutor.isStrategyRunning()
      console.log('策略执行器运行状态:', isRunning)
      
      if (isRunning) {
        const status = strategyExecutor.getStrategyStatus()
        console.log('策略执行状态:', status)
      }
    } else {
      console.error('❌ strategyExecutor 未定义')
    }
  } catch (error) {
    console.error('❌ 策略执行器检查失败:', error)
  }
  
  // 7. 网络连接检查
  console.log('\n🌐 7. 网络连接检查:')
  try {
    const testUrls = [
      'https://api.binance.com/api/v3/ping',
      'https://fapi.binance.com/fapi/v1/ping'
    ]
    
    for (const url of testUrls) {
      try {
        const response = await fetch(url)
        console.log(`${url}: ${response.ok ? '✅ 正常' : '❌ 失败'}`)
      } catch (error) {
        console.log(`${url}: ❌ 网络错误 - ${error.message}`)
      }
    }
  } catch (error) {
    console.error('❌ 网络检查失败:', error)
  }
  
  console.log('\n📋 ===== 诊断完成 =====\n')
}

// 快速修复策略启动问题
async function quickFixStartup() {
  console.log('\n🔧 ===== 快速修复启动问题 =====')
  
  try {
    // 1. 重置localStorage状态
    console.log('🔄 重置localStorage状态...')
    localStorage.setItem('trading_status', 'inactive')
    localStorage.setItem('selected_symbol', 'BTCUSDT')
    
    // 2. 停止所有策略
    console.log('🛑 停止所有策略...')
    if (typeof strategyStateService !== 'undefined') {
      await strategyStateService.stopAllStrategies()
    }
    
    // 3. 重新初始化数据库连接
    console.log('💾 重新初始化数据库...')
    // 数据库会自动初始化
    
    // 4. 检查API配置
    console.log('📡 检查API配置...')
    const apiConfig = localStorage.getItem('binance_api_config')
    if (!apiConfig) {
      console.warn('⚠️ 未找到API配置，请先在系统设置中配置API凭证')
    } else {
      const config = JSON.parse(apiConfig)
      if (!config.apiKey || !config.secretKey) {
        console.warn('⚠️ API配置不完整，请检查系统设置')
      } else {
        console.log('✅ API配置正常')
      }
    }
    
    // 5. 重新连接API
    console.log('🔌 重新连接API...')
    if (typeof binanceApi !== 'undefined' && apiConfig) {
      try {
        const config = JSON.parse(apiConfig)
        await binanceApi.connect(config.apiKey, config.secretKey, config.testnet || false)
        console.log('✅ API重新连接成功')
      } catch (error) {
        console.error('❌ API重新连接失败:', error)
      }
    }
    
    console.log('\n✅ 快速修复完成，请重新尝试启动策略')
    
  } catch (error) {
    console.error('❌ 快速修复失败:', error)
  }
}

// 模拟策略启动流程
async function simulateStartup() {
  console.log('\n🧪 ===== 模拟策略启动流程 =====')
  
  try {
    const selectedSymbol = 'BTCUSDT'
    
    // 模拟表单配置
    const formConfig = {
      capitalRatio: 25,
      initialCapital: 10000,
      maxSingleTrade: 2500,
      scalpingWeight: 50.6,
      trendWeight: 39.2,
      gridWeight: 9.5,
      superTrendWeight: 0.7,
      gridSpacing: 0.4,
      scalpProfit: 0.08,
      trendFastProfit: 0.2
    }
    
    console.log('📋 使用配置:', formConfig)
    
    // 1. 检查API连接
    console.log('1️⃣ 检查API连接...')
    if (typeof binanceApi === 'undefined') {
      console.error('❌ binanceApi未定义')
      return
    }
    
    const isConnected = binanceApi.getConnectionStatus()
    if (!isConnected) {
      console.error('❌ API未连接')
      return
    }
    console.log('✅ API已连接')
    
    // 2. 准备策略配置
    console.log('2️⃣ 准备策略配置...')
    const strategyConfigForDB = {
      symbol: selectedSymbol,
      isActive: true,
      parameters: formConfig,
      riskConfig: {
        maxDrawdownLimit: 35,
        maxPositionRatio: 98,
        profitProtectionLine: 15,
        maxLeverageRisk: 3.0
      },
      runtime: {
        lastHeartbeat: Date.now(),
        currentStatus: 'active',
        totalTrades: 0,
        realizedPnl: 0,
        unrealizedPnl: 0
      }
    }
    
    // 3. 保存到数据库
    console.log('3️⃣ 保存策略配置到数据库...')
    if (typeof strategyStateService !== 'undefined') {
      const dbSaveSuccess = await strategyStateService.saveAndActivateStrategy(strategyConfigForDB)
      if (dbSaveSuccess) {
        console.log('✅ 数据库保存成功')
      } else {
        console.error('❌ 数据库保存失败')
      }
    }
    
    // 4. 启动策略执行器
    console.log('4️⃣ 启动策略执行器...')
    if (typeof strategyExecutor !== 'undefined') {
      try {
        const strategySuccess = await strategyExecutor.startStrategy(selectedSymbol)
        if (strategySuccess) {
          console.log('✅ 策略执行器启动成功')
        } else {
          console.error('❌ 策略执行器启动失败')
        }
      } catch (error) {
        console.error('❌ 策略执行器启动异常:', error)
      }
    }
    
    // 5. 启动API交易
    console.log('5️⃣ 启动API交易...')
    try {
      await binanceApi.startTrading()
      console.log('✅ API交易启动成功')
    } catch (error) {
      console.error('❌ API交易启动失败:', error)
    }
    
    console.log('\n🎉 模拟启动流程完成')
    
  } catch (error) {
    console.error('❌ 模拟启动失败:', error)
  }
}

// 暴露调试函数
window.diagnoseStartup = diagnoseStartupFailure
window.quickFixStartup = quickFixStartup
window.simulateStartup = simulateStartup

console.log(`
📋 可用的诊断命令:
• diagnoseStartup()     - 完整诊断启动问题
• quickFixStartup()     - 快速修复常见问题
• simulateStartup()     - 模拟策略启动流程

使用方法:
在控制台输入命令并按回车执行
`)

// 自动运行初始诊断
diagnoseStartupFailure() 