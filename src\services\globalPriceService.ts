// 全局价格数据服务 - 策略导向的精简版WebSocket连接

import { binanceWebSocket, TickerData } from './binanceWebSocket'

export interface PriceDataState {
  currentPrice: number
  bid: number
  ask: number
  volume24h: number
  change24h: number
  high24h: number
  low24h: number
  lastUpdate: number
  isConnected: boolean
}

class GlobalPriceService {
  private currentState: PriceDataState = {
    currentPrice: 0,
    bid: 0,
    ask: 0,
    volume24h: 0,
    change24h: 0,
    high24h: 0,
    low24h: 0,
    lastUpdate: 0,
    isConnected: false
  }

  private subscribers: ((state: PriceDataState) => void)[] = []
  private currentSymbol: string | null = null
  private isMonitoring: boolean = false

  constructor() {
    console.log('🚀 策略导向价格服务初始化 - 精简版WebSocket')
    
    // 🔧 确保有默认币种
    this.ensureDefaultSymbol()
    
    // 应用启动时检查交易状态
    this.checkAndStartMonitoring()
    
    // 监听localStorage变化
    this.setupStorageListener()
  }

  // 确保有默认币种
  private ensureDefaultSymbol(): void {
    const currentSymbol = localStorage.getItem('selected_symbol')
    if (!currentSymbol || currentSymbol === 'null') {
      console.log('🔧 设置默认币种: BTCUSDT')
      localStorage.setItem('selected_symbol', 'BTCUSDT')
    }
  }

  // 检查并启动监控
  private checkAndStartMonitoring(): void {
    const tradingStatus = localStorage.getItem('trading_status')
    const symbol = localStorage.getItem('selected_symbol')
    
    console.log('📊 检查交易状态:', { tradingStatus, symbol })
    
    // 🔧 修复：添加展示模式，即使非交易状态也可以监控价格
    if (symbol && symbol !== 'null') {
      if (tradingStatus === 'active') {
        console.log('🎯 启动交易模式价格监控')
        this.startMonitoring(symbol)
      } else {
        // 启动纯展示模式
        console.log('📺 启动展示模式价格监控')
        this.startMonitoring(symbol, { displayOnly: true })
      }
    } else {
      this.stopMonitoring()
    }
  }

  // 🔧 安全修复：移除localStorage.setItem重写，改用轮询和事件监听
  private setupStorageListener(): void {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'trading_status' || e.key === 'selected_symbol') {
        console.log('🔄 交易状态变化:', e.key, e.oldValue, '->', e.newValue)
        setTimeout(() => this.checkAndStartMonitoring(), 200)
      }
    }
    
    window.addEventListener('storage', handleStorageChange)
    
    // 🔧 安全修复：使用轮询代替localStorage.setItem重写
    // 监听当前页面的localStorage变化 - 使用轮询方式
    let lastTradingStatus = localStorage.getItem('trading_status')
    let lastSelectedSymbol = localStorage.getItem('selected_symbol')
    
    const pollStorageChanges = () => {
      const currentTradingStatus = localStorage.getItem('trading_status')
      const currentSelectedSymbol = localStorage.getItem('selected_symbol')
      
      if (currentTradingStatus !== lastTradingStatus) {
        console.log('📝 检测到trading_status变化:', lastTradingStatus, '->', currentTradingStatus)
        lastTradingStatus = currentTradingStatus
        setTimeout(() => this.checkAndStartMonitoring(), 200)
      }
      
      if (currentSelectedSymbol !== lastSelectedSymbol) {
        console.log('📝 检测到selected_symbol变化:', lastSelectedSymbol, '->', currentSelectedSymbol)
        lastSelectedSymbol = currentSelectedSymbol
        setTimeout(() => this.checkAndStartMonitoring(), 200)
      }
    }
    
    // 每500ms检查一次localStorage变化
    setInterval(pollStorageChanges, 500)
    
    // 添加页面可见性监听，确保页面激活时重新检查
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden) {
        console.log('👁️ 页面变为可见，重新检查交易状态')
        setTimeout(() => this.checkAndStartMonitoring(), 500)
      }
    })
  }

  // 开始监控 - 支持交易模式和展示模式
  private startMonitoring(symbol: string, options: { displayOnly?: boolean } = {}): void {
    if (this.isMonitoring && this.currentSymbol === symbol) {
      console.log(`✅ 已在监控 ${symbol}，跳过重复启动`)
      return
    }

    const mode = options.displayOnly ? '展示模式' : '交易模式'
    console.log(`🎯 启动${mode}价格监控: ${symbol}`)
    
    // 停止之前的监控
    this.stopMonitoring()
    
    this.currentSymbol = symbol
    this.isMonitoring = true
    
    // 重置状态
    this.currentState = {
      currentPrice: 0,
      bid: 0,
      ask: 0,
      volume24h: 0,
      change24h: 0,
      high24h: 0,
      low24h: 0,
      lastUpdate: Date.now(),
      isConnected: false
    }

    // 启动WebSocket监控
    binanceWebSocket.startMonitoring(symbol)

    // 🔧 修复: 订阅Ticker数据 - 获取价格、成交量、涨跌幅
    binanceWebSocket.subscribeTicker(symbol, (data: TickerData) => {
      console.log('🔍 [DEBUG] 收到原始Ticker数据:', data)

      // 🔧 修复: 简化数据有效性检查，避免过度验证
      if (!data || !data.symbol) {
        console.warn('⚠️ 收到无效Ticker数据 - 缺少基本字段:', data)
        return
      }

      // 🔧 修复: 更新基础价格数据，确保数据有效性
      const newPrice = this.safeNumber(data.price)
      const newVolume = this.safeNumber(data.volume24h)
      const newChange = this.safeNumber(data.change24h)
      const newHigh = this.safeNumber(data.high24h)
      const newLow = this.safeNumber(data.low24h)

      console.log('🔍 [DEBUG] 解析后的数据:', {
        newPrice,
        newVolume,
        newChange,
        newHigh,
        newLow,
        isValidPrice: newPrice > 0
      })

      // 🔧 修复: 放宽价格验证条件，只要价格大于0就更新
      if (newPrice > 0) {
        console.log('✅ [UPDATE] 更新价格数据:', {
          oldPrice: this.currentState.currentPrice,
          newPrice: newPrice,
          symbol: data.symbol
        })

        this.currentState.currentPrice = newPrice
        this.currentState.volume24h = newVolume
        this.currentState.change24h = newChange
        this.currentState.high24h = newHigh > 0 ? newHigh : newPrice
        this.currentState.low24h = newLow > 0 ? newLow : newPrice
        this.currentState.lastUpdate = Date.now()
        this.currentState.isConnected = true

        console.log('🔔 [NOTIFY] 通知订阅者，当前状态:', {
          price: this.currentState.currentPrice,
          subscriberCount: this.subscribers.length
        })

        this.notifySubscribers()
      } else {
        console.warn('⚠️ 价格数据无效，忽略更新:', {
          rawPrice: data.price,
          parsedPrice: newPrice,
          dataType: typeof data.price
        })
      }
    })

    // 🔧 修复: 订阅BookTicker数据 - 获取实时买卖价
    binanceWebSocket.subscribeBookTicker(symbol, (data) => {
      console.log('🔍 [DEBUG] 收到原始BookTicker数据:', data)

      // 🔧 修复: 简化买卖价数据有效性检查
      if (!data || !data.symbol) {
        console.warn('⚠️ 收到无效买卖价数据 - 缺少基本字段:', data)
        return
      }

      const newBid = this.safeNumber(data.bid)
      const newAsk = this.safeNumber(data.ask)

      console.log('🔍 [DEBUG] 解析后的买卖价:', {
        newBid,
        newAsk,
        isValidBid: newBid > 0,
        isValidAsk: newAsk > 0,
        isValidSpread: newAsk > newBid
      })

      // 🔧 修复: 放宽买卖价验证条件
      if (newBid > 0 && newAsk > 0 && newAsk > newBid) {
        console.log('✅ [UPDATE] 更新买卖价数据:', {
          oldBid: this.currentState.bid,
          oldAsk: this.currentState.ask,
          newBid: newBid,
          newAsk: newAsk,
          spread: (newAsk - newBid).toFixed(4)
        })

        // 更新买卖价数据
        this.currentState.bid = newBid
        this.currentState.ask = newAsk
        this.currentState.lastUpdate = Date.now()

        console.log('🔔 [NOTIFY] 通知订阅者买卖价更新')
        this.notifySubscribers()
      } else {
        console.warn('⚠️ 买卖价数据不合理，忽略更新:', {
          rawBid: data.bid,
          rawAsk: data.ask,
          parsedBid: newBid,
          parsedAsk: newAsk,
          bidType: typeof data.bid,
          askType: typeof data.ask
        })
      }
    })

    console.log(`✅ 策略价格监控已启动: ${symbol} (Ticker + BookTicker双流)`)
  }

  // 安全数字处理，避免NaN
  private safeNumber(value: any): number {
    const num = parseFloat(value)
    return isNaN(num) ? 0 : num
  }

  // 停止监控
  private stopMonitoring(): void {
    if (!this.isMonitoring) {
      return
    }

    console.log('🛑 停止策略价格监控')
    
    this.isMonitoring = false
    this.currentSymbol = null
    
    binanceWebSocket.stopMonitoring()
    
    this.currentState.isConnected = false
    this.notifySubscribers()
  }

  // 订阅价格数据更新
  subscribe(callback: (state: PriceDataState) => void): () => void {
    this.subscribers.push(callback)
    
    // 立即发送当前状态
    callback(this.currentState)
    
    // 返回取消订阅函数
    return () => {
      const index = this.subscribers.indexOf(callback)
      if (index > -1) {
        this.subscribers.splice(index, 1)
      }
    }
  }

  // 通知所有订阅者
  private notifySubscribers(): void {
    console.log('🔔 [NOTIFY] 开始通知订阅者:', {
      subscriberCount: this.subscribers.length,
      currentState: {
        price: this.currentState.currentPrice,
        bid: this.currentState.bid,
        ask: this.currentState.ask,
        isConnected: this.currentState.isConnected
      }
    })

    this.subscribers.forEach((callback, index) => {
      try {
        console.log(`🔔 [NOTIFY] 通知订阅者 ${index + 1}/${this.subscribers.length}`)
        callback(this.currentState)
        console.log(`✅ [NOTIFY] 订阅者 ${index + 1} 通知成功`)
      } catch (error) {
        console.error(`❌ [NOTIFY] 通知订阅者 ${index + 1} 失败:`, error)
      }
    })

    console.log('🔔 [NOTIFY] 所有订阅者通知完成')
  }

  // 获取当前状态（同步方法，供策略使用）
  getCurrentState(): PriceDataState {
    return { ...this.currentState }
  }

  // 获取当前价格（策略最常用）
  getCurrentPrice(): number {
    return this.currentState.currentPrice
  }

  // 获取买卖价差（策略分析用）
  getSpread(): number {
    const spread = this.currentState.ask - this.currentState.bid
    return isNaN(spread) ? 0 : spread
  }

  // 获取24h涨跌幅（策略趋势判断）
  getChange24h(): number {
    return this.currentState.change24h
  }

  // 获取24h成交量（策略活跃度判断）
  getVolume24h(): number {
    return this.currentState.volume24h
  }

  // 检查价格数据有效性（策略安全检查）
  isPriceValid(): boolean {
    return this.currentState.currentPrice > 0 && 
           !isNaN(this.currentState.currentPrice) &&
           this.currentState.isConnected
  }

  // 检查连接状态
  isConnected(): boolean {
    return this.currentState.isConnected && this.isMonitoring
  }

  // 获取当前监控币种
  getCurrentSymbol(): string | null {
    return this.currentSymbol
  }

  // 手动重连（供调试使用）
  reconnect(): void {
    console.log('🔄 手动重连策略价格服务')
    
    // 确保有币种设置
    this.ensureDefaultSymbol()
    
    // 重新检查并启动监控
    this.checkAndStartMonitoring()
    
    // 如果仍然没有currentSymbol，使用默认值
    if (!this.currentSymbol) {
      const symbol = localStorage.getItem('selected_symbol') || 'BTCUSDT'
      console.log('🔧 使用默认币种重连:', symbol)
      this.startMonitoring(symbol, { displayOnly: true })
    } else {
      console.log('🔄 使用当前币种重连:', this.currentSymbol)
      this.startMonitoring(this.currentSymbol)
    }
  }

  // 策略专用：获取完整交易数据
  getStrategyData() {
    return {
      symbol: this.currentSymbol,
      price: this.currentState.currentPrice,
      change24h: this.currentState.change24h,
      volume24h: this.currentState.volume24h,
      spread: this.getSpread(),
      isValid: this.isPriceValid(),
      timestamp: this.currentState.lastUpdate
    }
  }

  // 🔧 调试专用：强制更新价格数据
  forceUpdatePriceData(price: number, bid: number, ask: number) {
    console.log('🔧 [DEBUG] 强制更新价格数据:', { price, bid, ask })

    this.currentState.currentPrice = price
    this.currentState.bid = bid
    this.currentState.ask = ask
    this.currentState.lastUpdate = Date.now()
    this.currentState.isConnected = true

    console.log('🔔 [DEBUG] 强制通知订阅者')
    this.notifySubscribers()
  }

  // 🔧 调试专用：获取订阅者数量
  getSubscriberCount(): number {
    return this.subscribers.length
  }
}

// 创建全局实例
export const globalPriceService = new GlobalPriceService() 