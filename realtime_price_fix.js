// 🚨 实时价格诊断和修复脚本 - 一键解决价格卡住问题
console.log('🚀 启动实时价格全面诊断和修复系统...')

// === 1. 全面状态检查 ===
function checkCompleteStatus() {
  console.log('\n📊 ===== 完整状态检查 =====')
  
  // 检查localStorage状态
  const tradingStatus = localStorage.getItem('trading_status')
  const selectedSymbol = localStorage.getItem('selected_symbol')
  
  console.log('🗃️ localStorage状态:')
  console.log(`  trading_status: ${tradingStatus}`)
  console.log(`  selected_symbol: ${selectedSymbol}`)
  
  // 检查globalPriceService状态
  if (typeof globalPriceService !== 'undefined') {
    const isConnected = globalPriceService.isConnected()
    const currentSymbol = globalPriceService.getCurrentSymbol()
    const currentPrice = globalPriceService.getCurrentPrice()
    const currentState = globalPriceService.getCurrentState()
    const subscriberCount = globalPriceService.getSubscriberCount()
    
    console.log('\n🌐 globalPriceService状态:')
    console.log(`  连接状态: ${isConnected ? '✅ 已连接' : '❌ 未连接'}`)
    console.log(`  当前币种: ${currentSymbol || '未设置'}`)
    console.log(`  当前价格: $${currentPrice.toFixed(2)}`)
    console.log(`  买入价: $${currentState.bid.toFixed(2)}`)
    console.log(`  卖出价: $${currentState.ask.toFixed(2)}`)
    console.log(`  最后更新: ${new Date(currentState.lastUpdate).toLocaleTimeString()}`)
    console.log(`  订阅者数量: ${subscriberCount}`)
    
    // 检查数据时效性
    const dataAge = Date.now() - currentState.lastUpdate
    console.log(`  数据时效: ${(dataAge / 1000).toFixed(1)}秒前`)
    
    if (dataAge > 10000) {
      console.warn('⚠️ 数据超过10秒未更新，可能存在连接问题')
    }
    
    return {
      isValid: isConnected && currentPrice > 0 && subscriberCount > 0,
      issues: [
        !isConnected && '服务未连接',
        currentPrice <= 0 && '价格数据无效',
        subscriberCount === 0 && '没有UI订阅者',
        dataAge > 10000 && '数据过时'
      ].filter(Boolean)
    }
  } else {
    console.error('❌ globalPriceService未定义！')
    return { isValid: false, issues: ['globalPriceService未定义'] }
  }
}

// === 2. WebSocket直连测试 ===
function testDirectWebSocket() {
  console.log('\n🧪 ===== 直接WebSocket连接测试 =====')
  
  return new Promise((resolve) => {
    const symbol = 'BTCUSDT'
    const tickerWs = new WebSocket(`wss://fstream.binance.com/ws/${symbol.toLowerCase()}@ticker`)
    
    let messageReceived = false
    const timeout = setTimeout(() => {
      if (!messageReceived) {
        console.error('❌ WebSocket测试超时，网络连接可能有问题')
        tickerWs.close()
        resolve(false)
      }
    }, 10000)
    
    tickerWs.onopen = () => {
      console.log('✅ 直接WebSocket连接成功')
    }
    
    tickerWs.onmessage = (event) => {
      if (!messageReceived) {
        messageReceived = true
        clearTimeout(timeout)
        
        try {
          const data = JSON.parse(event.data)
          if (data.e === '24hrTicker') {
            console.log('✅ 接收到Ticker数据:', {
              symbol: data.s,
              price: parseFloat(data.c).toFixed(2),
              change: parseFloat(data.P).toFixed(2) + '%',
              time: new Date().toLocaleTimeString()
            })
            tickerWs.close()
            resolve(true)
          }
        } catch (error) {
          console.error('❌ 数据解析失败:', error)
          tickerWs.close()
          resolve(false)
        }
      }
    }
    
    tickerWs.onerror = (error) => {
      console.error('❌ WebSocket连接错误:', error)
      clearTimeout(timeout)
      resolve(false)
    }
  })
}

// === 3. 强制修复和重连 ===
function forceFixAndReconnect() {
  console.log('\n🔧 ===== 强制修复和重连 =====')
  
  // 1. 重置localStorage状态
  console.log('🗃️ 重置localStorage状态...')
  localStorage.setItem('trading_status', 'active')
  localStorage.setItem('selected_symbol', 'BTCUSDT')
  console.log('✅ localStorage状态已重置')
  
  // 2. 清理全局价格服务
  if (typeof globalPriceService !== 'undefined') {
    console.log('🔄 重启globalPriceService...')
    
    // 强制重连
    globalPriceService.reconnect()
    
    console.log('✅ globalPriceService已重启')
    
    // 等待3秒检查结果
    return new Promise((resolve) => {
      setTimeout(() => {
        const newState = globalPriceService.getCurrentState()
        const success = newState.isConnected && newState.currentPrice > 0
        
        console.log('📊 重连结果:', {
          success: success ? '✅ 成功' : '❌ 失败',
          price: newState.currentPrice.toFixed(2),
          connected: newState.isConnected,
          subscribers: globalPriceService.getSubscriberCount()
        })
        
        resolve(success)
      }, 3000)
    })
  } else {
    console.error('❌ globalPriceService未定义，无法修复')
    return Promise.resolve(false)
  }
}

// === 4. 实时价格监控 ===
function startRealtimeMonitoring() {
  console.log('\n📡 ===== 启动实时价格监控 =====')
  
  if (typeof globalPriceService === 'undefined') {
    console.error('❌ globalPriceService未定义，无法启动监控')
    return
  }
  
  let lastPrice = null
  let lastBid = null
  let lastAsk = null
  let updateCount = 0
  
  const monitor = setInterval(() => {
    const state = globalPriceService.getCurrentState()
    
    // 检查价格更新
    if (state.currentPrice !== lastPrice) {
      updateCount++
      console.log(`💰 价格更新 #${updateCount}: ${lastPrice || 'N/A'} → $${state.currentPrice.toFixed(2)} (${new Date().toLocaleTimeString()})`)
      lastPrice = state.currentPrice
    }
    
    // 检查买卖价更新
    if (state.bid !== lastBid || state.ask !== lastAsk) {
      console.log(`📊 买卖价更新: 买${state.bid.toFixed(2)} / 卖${state.ask.toFixed(2)} (价差: ${(state.ask - state.bid).toFixed(4)})`)
      lastBid = state.bid
      lastAsk = state.ask
    }
    
    // 每10秒显示一次状态
    if (updateCount % 10 === 0) {
      console.log('📈 监控状态:', {
        连接状态: state.isConnected ? '✅ 正常' : '❌ 断开',
        价格: '$' + state.currentPrice.toFixed(2),
        更新次数: updateCount,
        数据时效: ((Date.now() - state.lastUpdate) / 1000).toFixed(1) + '秒前'
      })
    }
  }, 1000)
  
  // 保存到全局变量
  window.priceMonitor = monitor
  console.log('✅ 实时监控已启动')
  console.log('💡 停止监控: clearInterval(window.priceMonitor)')
  
  // 30秒后自动停止
  setTimeout(() => {
    clearInterval(monitor)
    console.log('⏰ 实时监控已自动停止')
    console.log(`📊 监控结果: 共检测到 ${updateCount} 次价格更新`)
  }, 30000)
}

// === 5. UI订阅测试 ===
function testUISubscription() {
  console.log('\n🎯 ===== UI订阅功能测试 =====')
  
  if (typeof globalPriceService === 'undefined') {
    console.error('❌ globalPriceService未定义')
    return
  }
  
  let callbackCount = 0
  
  console.log('📝 创建测试订阅...')
  const unsubscribe = globalPriceService.subscribe((state) => {
    callbackCount++
    console.log(`🔔 测试回调 #${callbackCount}:`, {
      price: state.currentPrice.toFixed(2),
      connected: state.isConnected,
      time: new Date(state.lastUpdate).toLocaleTimeString()
    })
  })
  
  console.log('✅ 测试订阅已创建')
  console.log('📊 当前订阅者数量:', globalPriceService.getSubscriberCount())
  
  // 10秒后取消订阅
  setTimeout(() => {
    unsubscribe()
    console.log(`📝 测试订阅已取消，共收到 ${callbackCount} 次回调`)
    console.log('📊 取消后订阅者数量:', globalPriceService.getSubscriberCount())
  }, 10000)
}

// === 6. 主要诊断和修复流程 ===
async function runCompleteDiagnosis() {
  console.log('🚀 ===== 开始完整诊断和修复流程 =====')
  
  // 步骤1: 检查当前状态
  const status = checkCompleteStatus()
  
  if (status.isValid) {
    console.log('✅ 系统状态正常，价格数据有效')
    console.log('💡 如果UI仍然显示旧价格，可能是页面刷新问题')
    startRealtimeMonitoring()
    return
  }
  
  console.log('⚠️ 发现问题:', status.issues.join(', '))
  
  // 步骤2: 测试网络连接
  console.log('\n🔍 测试网络连接...')
  const networkOk = await testDirectWebSocket()
  
  if (!networkOk) {
    console.error('❌ 网络连接有问题，请检查网络或防火墙设置')
    return
  }
  
  // 步骤3: 强制修复
  console.log('\n🔧 执行强制修复...')
  const fixSuccess = await forceFixAndReconnect()
  
  if (fixSuccess) {
    console.log('✅ 修复成功！价格数据恢复正常')
    
    // 启动监控验证
    testUISubscription()
    startRealtimeMonitoring()
  } else {
    console.error('❌ 修复失败，建议重新加载页面')
    console.log('💡 请尝试: location.reload()')
  }
}

// === 7. 紧急重置功能 ===
function emergencyReset() {
  console.log('🚨 ===== 紧急重置 =====')
  console.log('🔄 正在重置所有状态...')
  
  // 重置localStorage
  localStorage.setItem('trading_status', 'active')
  localStorage.setItem('selected_symbol', 'BTCUSDT')
  
  // 强制更新价格数据（如果服务可用）
  if (typeof globalPriceService !== 'undefined') {
    // 使用调试功能强制更新
    const testPrice = 104000 + Math.random() * 1000 // 模拟合理价格
    globalPriceService.forceUpdatePriceData(testPrice, testPrice - 0.5, testPrice + 0.5)
    console.log('🔧 已强制更新价格数据')
  }
  
  // 建议刷新页面
  console.log('💡 建议刷新页面: location.reload()')
  
  // 3秒后自动刷新
  setTimeout(() => {
    if (confirm('是否要自动刷新页面以完成重置？')) {
      location.reload()
    }
  }, 3000)
}

// === 导出函数到全局 ===
window.checkCompleteStatus = checkCompleteStatus
window.testDirectWebSocket = testDirectWebSocket
window.forceFixAndReconnect = forceFixAndReconnect
window.startRealtimeMonitoring = startRealtimeMonitoring
window.testUISubscription = testUISubscription
window.runCompleteDiagnosis = runCompleteDiagnosis
window.emergencyReset = emergencyReset

// === 显示使用说明 ===
console.log('\n🎯 ===== 实时价格修复工具已就绪 =====')
console.log('📋 可用命令:')
console.log('  runCompleteDiagnosis() - 🚀 完整诊断和自动修复（推荐）')
console.log('  checkCompleteStatus() - 📊 检查当前状态')
console.log('  testDirectWebSocket() - 🧪 测试网络连接')
console.log('  forceFixAndReconnect() - 🔧 强制修复和重连')
console.log('  startRealtimeMonitoring() - 📡 启动实时监控')
console.log('  testUISubscription() - 🎯 测试UI订阅')
console.log('  emergencyReset() - 🚨 紧急重置（最后手段）')
console.log('')
console.log('💡 建议直接运行: runCompleteDiagnosis()')
console.log('🔥 如果问题严重，使用: emergencyReset()')

// 自动执行快速检查
setTimeout(() => {
  console.log('\n🔍 自动执行快速状态检查...')
  const status = checkCompleteStatus()
  
  if (!status.isValid) {
    console.log('\n⚠️ 检测到问题，建议运行完整诊断:')
    console.log('runCompleteDiagnosis()')
  } else {
    console.log('\n✅ 快速检查通过，系统状态正常')
    console.log('💡 如果价格仍然不更新，请运行: startRealtimeMonitoring()')
  }
}, 2000) 