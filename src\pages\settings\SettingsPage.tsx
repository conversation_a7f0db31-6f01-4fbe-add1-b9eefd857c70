import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Settings, 
  Key, 
  Shield, 
  Bell, 
  Palette,
  Eye,
  EyeOff,
  Save,
  RefreshCw,
  CheckCircle,
  XCircle,
  AlertCircle,
  X
} from 'lucide-react'
import { strategyStateService } from '@/services/strategyStateService'

// 🔧 确保strategyStateService全局可用
if (typeof window !== 'undefined') {
  (window as any).strategyStateService = strategyStateService
}

interface ApiConfig {
  exchange: string
  apiKey: string
  secretKey: string
  testnet: boolean
  useProxy: boolean
  proxyHost: string
  proxyPort: string
}

interface NotificationConfig {
  email: string
  emailEnabled: boolean
  smsEnabled: boolean
  pushEnabled: boolean
  riskAlerts: boolean
  tradeAlerts: boolean
}

interface NotificationMessage {
  id: string
  type: 'success' | 'error' | 'info'
  title: string
  message: string
}

export function SettingsPage() {
  const [showApiSecret, setShowApiSecret] = useState(false)
  const [showSecretKey, setShowSecretKey] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [notifications, setNotifications] = useState<NotificationMessage[]>([])

  // 显示通知
  const showNotification = (type: 'success' | 'error' | 'info', title: string, message: string) => {
    const notification: NotificationMessage = {
      id: Date.now().toString(),
      type,
      title,
      message
    }
    setNotifications(prev => [notification, ...prev.slice(0, 2)]) // 最多显示3条
    
    // 3秒后自动消失
    setTimeout(() => {
      setNotifications(prev => prev.filter(n => n.id !== notification.id))
    }, 3000)
  }

  // 关闭通知
  const dismissNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id))
  }

  // API配置状态
  const [apiConfig, setApiConfig] = useState<ApiConfig>({
    exchange: 'binance',
    apiKey: '',
    secretKey: '',
    testnet: true,
    useProxy: false,
    proxyHost: '127.0.0.1',
    proxyPort: '7890'
  })

  // 通知配置状态
  const [notificationConfig, setNotificationConfig] = useState<NotificationConfig>({
    email: '',
    emailEnabled: true,
    smsEnabled: false,
    pushEnabled: true,
    riskAlerts: true,
    tradeAlerts: false
  })

  // 🔧 加载保存的配置 - 优先从数据库读取
  useEffect(() => {
    const loadApiConfig = async () => {
      console.log('🔍 加载API配置...')
      
      let config = null
      
      // 1. 优先尝试从数据库读取
      if (typeof window !== 'undefined' && (window as any).strategyStateService) {
        try {
          const strategyStateService = (window as any).strategyStateService
          const dbConfig = await strategyStateService.getApiConfig()
          
          if (dbConfig) {
            console.log('✅ 从数据库加载API配置')
            config = {
              exchange: 'binance', // 数据库中暂时只支持币安
              apiKey: dbConfig.apiKey,
              secretKey: dbConfig.secretKey,
              testnet: dbConfig.testnet,
              useProxy: dbConfig.useProxy,
              proxyHost: dbConfig.proxyHost,
              proxyPort: dbConfig.proxyPort
            }
          }
        } catch (dbError) {
          console.warn('⚠️ 数据库配置读取失败，fallback到localStorage:', dbError)
        }
      }
      
      // 2. fallback到localStorage
      if (!config) {
        console.log('🔄 从localStorage读取API配置...')
        const savedConfig = localStorage.getItem('binance_api_config')
        if (savedConfig) {
          try {
            const parsedConfig = JSON.parse(savedConfig)
            config = {
              exchange: parsedConfig.exchange || 'binance',
              apiKey: parsedConfig.apiKey || '',
              secretKey: parsedConfig.secretKey || '',
              testnet: parsedConfig.testnet !== false, // 默认true
              useProxy: parsedConfig.useProxy || false,
              proxyHost: parsedConfig.proxyHost || '127.0.0.1',
              proxyPort: parsedConfig.proxyPort || '7890'
            }
            console.log('✅ 从localStorage加载API配置')
          } catch (error) {
            console.error('❌ localStorage配置解析失败:', error)
          }
        }
      }
      
      // 3. 应用配置或使用默认值
      if (config) {
        setApiConfig(config)
        console.log('📋 API配置已加载:', {
          ...config,
          apiKey: config.apiKey ? `${config.apiKey.substring(0, 8)}...` : '未设置',
          secretKey: config.secretKey ? `${config.secretKey.substring(0, 8)}...` : '未设置'
        })
      } else {
        console.log('⚪ 使用默认API配置')
        setApiConfig({
          exchange: 'binance',
          apiKey: '',
          secretKey: '',
          testnet: true,
          useProxy: false,
          proxyHost: '127.0.0.1',
          proxyPort: '7890'
        })
      }
    }
    
    loadApiConfig()
  }, [])

  const handleSaveApiConfig = async () => {
    setIsLoading(true)
    try {
      // 🔧 准备API配置数据
      const configToSave = {
        exchange: apiConfig.exchange,
        apiKey: apiConfig.apiKey,
        secretKey: apiConfig.secretKey,
        testnet: apiConfig.testnet,
        useProxy: apiConfig.useProxy,
        proxyHost: apiConfig.proxyHost,
        proxyPort: apiConfig.proxyPort
      }
      
      console.log('🔧 开始保存API配置，优先保存到数据库...')
      
      // 🔧 优先保存到数据库（如果有strategyStateService）
      let dbSaveSuccess = false
      if (typeof window !== 'undefined' && (window as any).strategyStateService) {
        try {
          const strategyStateService = (window as any).strategyStateService
          dbSaveSuccess = await strategyStateService.saveApiConfig({
            apiKey: apiConfig.apiKey,
            secretKey: apiConfig.secretKey,
            testnet: apiConfig.testnet,
            useProxy: apiConfig.useProxy,
            proxyHost: apiConfig.proxyHost,
            proxyPort: apiConfig.proxyPort
          })
          
          if (dbSaveSuccess) {
            console.log('✅ API配置已成功保存到数据库')
          } else {
            console.warn('⚠️ 数据库保存失败，fallback到localStorage')
          }
        } catch (dbError) {
          console.warn('⚠️ 数据库保存异常，fallback到localStorage:', dbError)
        }
      } else {
        console.warn('⚠️ strategyStateService不可用，使用localStorage保存')
      }
      
      // 🔧 保存到localStorage（数据库成功时作为备份，失败时作为主要存储）
      localStorage.setItem('binance_api_config', JSON.stringify(configToSave))
      console.log('💾 API配置已保存到localStorage (兼容模式)')
      
      // 如果有binanceApi实例，更新代理配置
      if (typeof window !== 'undefined' && (window as any).binanceApi) {
        const binanceApi = (window as any).binanceApi
        binanceApi.setProxyConfig(
          apiConfig.useProxy,
          apiConfig.proxyHost,
          apiConfig.proxyPort
        )
        console.log('🌐 binanceApi代理配置已更新')
      }
      
      // 🎉 根据保存结果显示不同的消息
      if (dbSaveSuccess) {
        showNotification('success', '保存成功', 'API配置已成功保存到数据库（包含localStorage备份）')
      } else {
        showNotification('success', '保存成功', 'API配置已保存到本地存储（数据库不可用）')
      }
      
      console.log('✅ API配置保存完成:', configToSave)
      
    } catch (error) {
      console.error('❌ API配置保存失败:', error)
      showNotification('error', '保存失败', '配置保存时发生错误，请重试')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSaveNotificationConfig = async () => {
    setIsLoading(true)
    try {
      console.log('保存通知配置:', notificationConfig)
      await new Promise(resolve => setTimeout(resolve, 1000))
      showNotification('success', '保存成功', '通知配置已成功保存')
    } catch (error) {
      showNotification('error', '保存失败', '配置保存时发生错误，请重试')
    } finally {
      setIsLoading(false)
    }
  }

  const testApiConnection = async () => {
    if (!apiConfig.apiKey || !apiConfig.secretKey) {
      showNotification('error', '配置不完整', '请先填写API Key和Secret Key')
      return
    }
    
    setIsLoading(true)
    try {
      // 获取或创建binanceApi实例
      let binanceApi = (window as any).binanceApi
      if (!binanceApi) {
        // 这里需要导入binanceApi实例
        const { binanceApi: apiInstance } = await import('@/services/binanceApi')
        binanceApi = apiInstance
        ;(window as any).binanceApi = binanceApi
      }
      
      // 设置代理配置
      binanceApi.setProxyConfig(
        apiConfig.useProxy,
        apiConfig.proxyHost,
        apiConfig.proxyPort
      )
      
      // 测试连接
      const connected = await binanceApi.connect(
        apiConfig.apiKey,
        apiConfig.secretKey,
        {
          enabled: apiConfig.useProxy,
          host: apiConfig.proxyHost,
          port: apiConfig.proxyPort
        }
      )
      
      if (connected) {
        const proxyMsg = apiConfig.useProxy ? ` (通过代理 ${apiConfig.proxyHost}:${apiConfig.proxyPort})` : ''
        showNotification('success', '连接成功', `API连接测试成功！${proxyMsg}`)
      } else {
        showNotification('error', '连接失败', 'API连接测试失败，请检查配置')
      }
    } catch (error) {
      console.error('连接测试失败:', error)
      showNotification('error', '连接失败', 'API连接测试失败，请检查配置')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* 通知区域 */}
      {notifications.length > 0 && (
        <div className="space-y-3">
          {notifications.map((notification) => (
            <Card key={notification.id} className={`${
              notification.type === 'success' ? 'border-green-500/30 bg-green-500/10' :
              notification.type === 'error' ? 'border-red-500/30 bg-red-500/10' :
              'border-blue-500/30 bg-blue-500/10'
            }`}>
              <CardContent className="pt-4">
                <div className="flex items-start gap-3">
                  {notification.type === 'success' ? (
                    <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                  ) : notification.type === 'error' ? (
                    <XCircle className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" />
                  ) : (
                    <AlertCircle className="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" />
                  )}
                  <div className="flex-1">
                    <p className={`font-medium ${
                      notification.type === 'success' ? 'text-green-400' :
                      notification.type === 'error' ? 'text-red-400' :
                      'text-blue-400'
                    }`}>
                      {notification.title}
                    </p>
                    <p className="text-sm text-slate-300 mt-1">
                      {notification.message}
                    </p>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => dismissNotification(notification.id)}
                    className="p-1 h-6 w-6"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* 页面标题 */}
      <div className="flex items-center gap-3">
        <Settings className="w-6 h-6" />
        <div>
          <h1 className="text-2xl font-bold">系统设置</h1>
          <p className="text-muted-foreground">配置系统基础参数和连接信息</p>
        </div>
      </div>

      {/* 设置选项卡 */}
      <Tabs defaultValue="api" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="api" className="flex items-center gap-2">
            <Key className="w-4 h-4" />
            API配置
          </TabsTrigger>
          <TabsTrigger value="notifications" className="flex items-center gap-2">
            <Bell className="w-4 h-4" />
            通知设置
          </TabsTrigger>
          <TabsTrigger value="general" className="flex items-center gap-2">
            <Palette className="w-4 h-4" />
            通用设置
          </TabsTrigger>
        </TabsList>

        {/* API配置 */}
        <TabsContent value="api" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Key className="w-5 h-5" />
                交易所API配置
              </CardTitle>
              <CardDescription>
                配置交易所API密钥，用于数据获取和交易执行
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* 交易所选择 */}
              <div className="space-y-2">
                <Label htmlFor="exchange">交易所</Label>
                <Select 
                  value={apiConfig.exchange} 
                  onValueChange={(value: string) => setApiConfig({...apiConfig, exchange: value})}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择交易所" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="binance">Binance</SelectItem>
                    <SelectItem value="okx">OKX</SelectItem>
                    <SelectItem value="huobi">Huobi</SelectItem>
                    <SelectItem value="gate">Gate.io</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* API Key */}
              <div className="space-y-2">
                <Label htmlFor="apiKey">API Key</Label>
                <div className="relative">
                  <Input
                    id="apiKey"
                    type={showApiSecret ? 'text' : 'password'}
                    value={apiConfig.apiKey}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => setApiConfig({...apiConfig, apiKey: e.target.value})}
                    placeholder="输入API Key"
                    className="pr-10"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowApiSecret(!showApiSecret)}
                  >
                    {showApiSecret ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </Button>
                </div>
              </div>

              {/* Secret Key */}
              <div className="space-y-2">
                <Label htmlFor="secretKey">Secret Key</Label>
                <div className="relative">
                  <Input
                    id="secretKey"
                    type={showSecretKey ? 'text' : 'password'}
                    value={apiConfig.secretKey}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => setApiConfig({...apiConfig, secretKey: e.target.value})}
                    placeholder="输入Secret Key"
                    className="pr-10"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowSecretKey(!showSecretKey)}
                  >
                    {showSecretKey ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </Button>
                </div>
              </div>

              {/* 测试网络 */}
              <div className="flex items-center space-x-2">
                <Switch
                  id="testnet"
                  checked={apiConfig.testnet}
                  onCheckedChange={(checked: boolean) => setApiConfig({...apiConfig, testnet: checked})}
                />
                <Label htmlFor="testnet">使用测试网络</Label>
              </div>

              {/* 代理设置 */}
              <div className="space-y-4 pt-4 border-t">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="useProxy"
                    checked={apiConfig.useProxy}
                    onCheckedChange={(checked: boolean) => setApiConfig({...apiConfig, useProxy: checked})}
                  />
                  <Label htmlFor="useProxy">使用Clash代理</Label>
                </div>
                
                {apiConfig.useProxy && (
                  <div className="grid grid-cols-2 gap-4 pl-6">
                    <div className="space-y-2">
                      <Label htmlFor="proxyHost">代理地址</Label>
                      <Input
                        id="proxyHost"
                        value={apiConfig.proxyHost}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => setApiConfig({...apiConfig, proxyHost: e.target.value})}
                        placeholder="127.0.0.1"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="proxyPort">端口</Label>
                      <Input
                        id="proxyPort"
                        value={apiConfig.proxyPort}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => setApiConfig({...apiConfig, proxyPort: e.target.value})}
                        placeholder="7890"
                      />
                    </div>
                  </div>
                )}
              </div>

              {/* 操作按钮 */}
              <div className="flex gap-3 pt-4">
                <Button onClick={handleSaveApiConfig} disabled={isLoading}>
                  {isLoading ? <RefreshCw className="w-4 h-4 mr-2 animate-spin" /> : <Save className="w-4 h-4 mr-2" />}
                  保存配置
                </Button>
                <Button variant="outline" onClick={testApiConnection} disabled={isLoading}>
                  <Shield className="w-4 h-4 mr-2" />
                  测试连接
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* API权限提醒 */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-start gap-3">
                <Shield className="w-5 h-5 text-amber-400 mt-0.5 flex-shrink-0" />
                <div className="space-y-3">
                  <p className="font-medium text-white">安全提醒</p>
                  <div className="space-y-2 text-sm text-slate-300">
                    <div className="flex items-center gap-2">
                      <div className="w-1 h-1 bg-slate-400 rounded-full" />
                      <span>API密钥仅需要现货交易和读取权限</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-1 h-1 bg-slate-400 rounded-full" />
                      <span>建议优先使用测试网络验证配置</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-1 h-1 bg-slate-400 rounded-full" />
                      <span>定期更换API密钥确保账户安全</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-1 h-1 bg-slate-400 rounded-full" />
                      <span>不要在公共场所或设备上操作</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-1 h-1 bg-slate-400 rounded-full" />
                      <span>代理功能适用于无法直接访问币安API的网络环境</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 代理配置说明 */}
          {apiConfig.useProxy && (
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-start gap-3">
                  <Settings className="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" />
                  <div className="space-y-3">
                    <p className="font-medium text-white">代理配置说明</p>
                    <div className="space-y-2 text-sm text-slate-300">
                      <div className="flex items-center gap-2">
                        <div className="w-1 h-1 bg-slate-400 rounded-full" />
                        <span>请确保Clash已启动并配置了正确的代理规则</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-1 h-1 bg-slate-400 rounded-full" />
                        <span>默认代理地址为 127.0.0.1:7890 (Clash默认HTTP端口)</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-1 h-1 bg-slate-400 rounded-full" />
                        <span>如需修改代理地址，请确认端口开放并可访问</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-1 h-1 bg-slate-400 rounded-full" />
                        <span>代理仅在生产环境下生效，测试模式下会忽略代理设置</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* 通知设置 */}
        <TabsContent value="notifications" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="w-5 h-5" />
                通知与告警设置
              </CardTitle>
              <CardDescription>
                配置各种通知方式和告警条件
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* 邮箱地址 */}
              <div className="space-y-2">
                <Label htmlFor="email">邮箱地址</Label>
                <Input
                  id="email"
                  type="email"
                  value={notificationConfig.email}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setNotificationConfig({...notificationConfig, email: e.target.value})}
                  placeholder="<EMAIL>"
                />
              </div>

              {/* 通知方式 */}
              <div className="space-y-4">
                <h4 className="text-sm font-medium">通知方式</h4>
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="emailEnabled"
                      checked={notificationConfig.emailEnabled}
                      onCheckedChange={(checked: boolean) => setNotificationConfig({...notificationConfig, emailEnabled: checked})}
                    />
                    <Label htmlFor="emailEnabled">邮件通知</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="smsEnabled"
                      checked={notificationConfig.smsEnabled}
                      onCheckedChange={(checked: boolean) => setNotificationConfig({...notificationConfig, smsEnabled: checked})}
                    />
                    <Label htmlFor="smsEnabled">短信通知</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="pushEnabled"
                      checked={notificationConfig.pushEnabled}
                      onCheckedChange={(checked: boolean) => setNotificationConfig({...notificationConfig, pushEnabled: checked})}
                    />
                    <Label htmlFor="pushEnabled">推送通知</Label>
                  </div>
                </div>
              </div>

              {/* 告警类型 */}
              <div className="space-y-4">
                <h4 className="text-sm font-medium">告警类型</h4>
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="riskAlerts"
                      checked={notificationConfig.riskAlerts}
                      onCheckedChange={(checked: boolean) => setNotificationConfig({...notificationConfig, riskAlerts: checked})}
                    />
                    <Label htmlFor="riskAlerts">风险告警</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="tradeAlerts"
                      checked={notificationConfig.tradeAlerts}
                      onCheckedChange={(checked: boolean) => setNotificationConfig({...notificationConfig, tradeAlerts: checked})}
                    />
                    <Label htmlFor="tradeAlerts">交易成交通知</Label>
                  </div>
                </div>
              </div>

              <div className="pt-4">
                <Button onClick={handleSaveNotificationConfig} disabled={isLoading}>
                  {isLoading ? <RefreshCw className="w-4 h-4 mr-2 animate-spin" /> : <Save className="w-4 h-4 mr-2" />}
                  保存通知配置
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 通用设置 */}
        <TabsContent value="general" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Palette className="w-5 h-5" />
                界面与显示设置
              </CardTitle>
              <CardDescription>
                自定义界面外观和显示偏好
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* 主题设置 */}
              <div className="space-y-2">
                <Label>主题模式</Label>
                <Select defaultValue="dark">
                  <SelectTrigger>
                    <SelectValue placeholder="选择主题" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="light">浅色模式</SelectItem>
                    <SelectItem value="dark">深色模式</SelectItem>
                    <SelectItem value="auto">跟随系统</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* 语言设置 */}
              <div className="space-y-2">
                <Label>界面语言</Label>
                <Select defaultValue="zh-CN">
                  <SelectTrigger>
                    <SelectValue placeholder="选择语言" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="zh-CN">简体中文</SelectItem>
                    <SelectItem value="en">English</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* 刷新频率 */}
              <div className="space-y-2">
                <Label>数据刷新频率</Label>
                <Select defaultValue="5">
                  <SelectTrigger>
                    <SelectValue placeholder="选择刷新频率" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1秒</SelectItem>
                    <SelectItem value="5">5秒</SelectItem>
                    <SelectItem value="10">10秒</SelectItem>
                    <SelectItem value="30">30秒</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* 高级设置 */}
              <div className="space-y-4">
                <h4 className="text-sm font-medium">高级设置</h4>
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Switch id="autoSave" defaultChecked />
                    <Label htmlFor="autoSave">自动保存配置</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch id="soundEnabled" defaultChecked />
                    <Label htmlFor="soundEnabled">启用声音提示</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch id="analyticsEnabled" defaultChecked />
                    <Label htmlFor="analyticsEnabled">发送匿名使用统计</Label>
                  </div>
                </div>
              </div>

              <div className="pt-4">
                <Button>
                  <Save className="w-4 h-4 mr-2" />
                  保存设置
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
} 