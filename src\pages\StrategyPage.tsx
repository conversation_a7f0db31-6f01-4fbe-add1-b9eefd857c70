import { useState, useEffect } from 'react'
import { binanceApi, LiveTradingMetrics } from '@/services/binanceApi'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from '@/components/ui/tabs'
import { 
  Play, 
  Pause, 
  Settings, 
  BarChart3, 
  AlertTriangle,
  Clock,
  Target,
  Shield,
  Zap,
  TrendingDown
} from 'lucide-react'
import { Switch } from '@/components/ui/switch'
import { strategyExecutor, StrategyStatus, STRATEGY_ULTIMATE_CONFIG } from '@/services/strategyExecutor'
import { strategyStatsService, StrategyStats, SystemStats } from '@/services/strategyStatsService'
import { strategyStateService, StrategyConfig } from '@/services/strategyStateService'

export function StrategyPage() {
  const [systemRunning, setSystemRunning] = useState<boolean>(false)
  const [selectedSymbol, setSelectedSymbol] = useState<string>('BTCUSDT')
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [statusMessage, setStatusMessage] = useState<string>('')
  const [lastOperationTime, setLastOperationTime] = useState<string>('')
  const [buttonClicked, setButtonClicked] = useState<boolean>(false)
  
  // 策略执行器状态
  const [strategyStatus, setStrategyStatus] = useState<StrategyStatus | null>(null)
  
  // 策略统计数据状态
  const [strategyStats, setStrategyStats] = useState<StrategyStats[]>([])
  const [systemStats, setSystemStats] = useState<SystemStats | null>(null)
  
  // 表单配置状态
  const [formConfig, setFormConfig] = useState({
    capitalRatio: 25,
    initialCapital: 10000,
    maxSingleTrade: 2500,
    scalpingWeight: 50.6,
    trendWeight: 39.2,
    gridWeight: 9.5,
    superTrendWeight: 0.7,
    gridSpacing: 0.4,
    scalpProfit: 0.08,
    trendFastProfit: 0.2
  })
  
  // 通用按钮点击反馈
  const handleButtonClick = () => {
    setButtonClicked(true)
    setTimeout(() => setButtonClicked(false), 150)
    
    // 添加触觉反馈（如果支持）
    if ('vibrate' in navigator) {
      navigator.vibrate(50)
    }
  }
  const [liveMetrics, setLiveMetrics] = useState<LiveTradingMetrics>({
    isConnected: false,
    isTrading: false,
    accountBalance: 0,
    availableBalance: 0,
    currentPosition: 0,
    unrealizedPnL: 0,
    realizedPnL: 0,
    todayTrades: 0,
    currentDrawdown: 0,
    maxDrawdownToday: 0,
    riskLevel: 'LOW'
  })
  
  // 更新实时数据
  const updateLiveData = async () => {
    if (binanceApi.getConnectionStatus()) {
      const metrics = await binanceApi.getLiveTradingMetrics()
      if (metrics) {
        setLiveMetrics(metrics)
        
        // 只有当 localStorage 状态与 API 状态不一致时才同步
        const currentTradingStatus = localStorage.getItem('trading_status')
        
        // 如果 API 显示正在交易，但 localStorage 不是 active，则同步
        if (metrics.isTrading && currentTradingStatus !== 'active') {
          localStorage.setItem('trading_status', 'active')
          setSystemRunning(true)
          console.log('API显示交易中，同步状态到active')
        }
        // 如果 API 显示未在交易，且 localStorage 是 active，则说明可能出现了断连
        else if (!metrics.isTrading && currentTradingStatus === 'active') {
          // 保持当前状态，不强制同步，避免覆盖用户操作
          console.log('API显示未交易，但localStorage为active，保持当前状态')
        }
        // 其他情况保持当前状态不变
      }
    }
  }

  // 初始化连接检查
  const checkInitialConnection = async () => {
    // 添加点击反馈
    handleButtonClick()
    
    setIsLoading(true)
    setStatusMessage('🔄 正在连接币安API...')
    
    try {
      // 🔧 优先从数据库读取API配置，fallback到localStorage
      console.log('🔍 查找API配置...')
      let config = null
      
      // 1. 尝试从数据库读取
      try {
        const dbConfig = await strategyStateService.getApiConfig()
        if (dbConfig) {
          console.log('✅ 从数据库加载API配置')
          config = {
            apiKey: dbConfig.apiKey,
            secretKey: dbConfig.secretKey,
            testnet: dbConfig.testnet,
            useProxy: dbConfig.useProxy,
            proxyHost: dbConfig.proxyHost,
            proxyPort: dbConfig.proxyPort
          }
        }
      } catch (dbError) {
        console.warn('⚠️ 数据库API配置读取失败，尝试localStorage:', dbError)
      }
      
      // 2. fallback到localStorage
      if (!config) {
        console.log('🔄 从localStorage读取API配置...')
        const savedConfig = localStorage.getItem('binance_api_config')
        if (!savedConfig) {
          throw new Error('未找到API配置，请先在风险管理页面配置')
        }
        config = JSON.parse(savedConfig)
      }
      
      if (!config.apiKey || !config.secretKey) {
        throw new Error('API密钥不完整，请检查配置')
      }
      
      // 🌐 配置API（自动检测代理配置）
      await binanceApi.connect(config.apiKey, config.secretKey, config.testnet || false)
      
      // ✅ binanceApi现在会自动检测并应用代理配置，无需手动设置
      
      // 更新连接状态
      await updateLiveData()
      
      // 检查并恢复之前的交易状态
      const savedTradingStatus = localStorage.getItem('trading_status')
      if (savedTradingStatus === 'active') {
        console.log('检测到之前的交易状态为active，恢复UI状态')
        setSystemRunning(true)
        setStatusMessage('✅ API连接成功，检测到交易状态已激活')
      } else {
        console.log('没有检测到活跃的交易状态')
        setSystemRunning(false)
        setStatusMessage('✅ API连接成功，可以开始配置交易')
      }
      
      setLastOperationTime(`连接时间: ${new Date().toLocaleTimeString()}`)
      setTimeout(() => setStatusMessage(''), 3000)
      
    } catch (error) {
      console.error('初始化连接失败:', error)
      setStatusMessage(`❌ 连接失败: ${error instanceof Error ? error.message : '未知错误'}`)
      setTimeout(() => setStatusMessage(''), 5000)
    } finally {
      setIsLoading(false)
    }
  }

  // 页面初始化
  useEffect(() => {
    // 🔧 确保策略执行器全局可访问
    ;(window as any).strategyExecutor = strategyExecutor
    console.log('✅ 策略执行器已设置为全局可访问')

    const initializeFromDatabase = async () => {
      // 🔧 从数据库恢复策略状态，而不是localStorage
      const activeStrategy = await strategyStateService.getActiveStrategy()

      if (activeStrategy) {
        console.log('🔄 从数据库恢复活跃策略:', {
          symbol: activeStrategy.symbol,
          status: activeStrategy.runtime.currentStatus,
          startedAt: new Date(activeStrategy.runtime.startedAt || 0).toLocaleString()
        })
        
        setSelectedSymbol(activeStrategy.symbol)
        setSystemRunning(activeStrategy.isActive && activeStrategy.runtime.currentStatus === 'active')
        
        // 恢复表单配置
        setFormConfig({
          capitalRatio: activeStrategy.parameters.capitalRatio,
          initialCapital: activeStrategy.parameters.initialCapital,
          maxSingleTrade: activeStrategy.parameters.maxSingleTrade,
          scalpingWeight: activeStrategy.parameters.scalpingWeight,
          trendWeight: activeStrategy.parameters.trendWeight,
          gridWeight: activeStrategy.parameters.gridWeight,
          superTrendWeight: activeStrategy.parameters.superTrendWeight,
          gridSpacing: activeStrategy.parameters.gridSpacing,
          scalpProfit: activeStrategy.parameters.scalpProfit,
          trendFastProfit: activeStrategy.parameters.trendFastProfit
        })
        
        console.log('✅ 策略状态已从数据库恢复')
      } else {
        console.log('⚪ 数据库中无活跃策略，使用默认配置')
        setSystemRunning(false)
        
        // 尝试从localStorage读取备选配置（向后兼容）
        const savedSymbol = localStorage.getItem('selected_symbol')
        if (savedSymbol) {
          setSelectedSymbol(savedSymbol)
        }
      }
      
      // 立即加载保存的配置
      loadSavedConfig()
      
      checkInitialConnection()
    }
    
    initializeFromDatabase()
  }, [])

  // 移除延迟加载配置的useEffect，改为在页面初始化时立即加载

  // 实时数据轮询
  useEffect(() => {
    let interval: NodeJS.Timeout | null = null
    
    if (liveMetrics.isConnected) {
      interval = setInterval(updateLiveData, 5000)
    }
    
    return () => {
      if (interval) clearInterval(interval)
    }
  }, [liveMetrics.isConnected])

  // 策略状态监控
  useEffect(() => {
    let strategyInterval: NodeJS.Timeout | null = null
    
    if (systemRunning && strategyExecutor.isStrategyRunning()) {
      // 启动策略统计服务
      strategyStatsService.startRealTimeStats()
      
      // 每2秒更新策略状态和统计数据
      strategyInterval = setInterval(() => {
        const status = strategyExecutor.getStrategyStatus()
        setStrategyStatus(status)
        
        // 更新策略统计数据
        const stats = strategyStatsService.getStrategyStats()
        const sysStats = strategyStatsService.getSystemStats()
        setStrategyStats(stats)
        setSystemStats(sysStats)
      }, 2000)
    } else {
      // 停止策略统计服务
      strategyStatsService.stopRealTimeStats()
    }
    
    return () => {
      if (strategyInterval) clearInterval(strategyInterval)
    }
  }, [systemRunning])

  // 币种选择处理
  const handleSymbolChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const value = event.target.value
    if (value === 'custom') {
      const customInput = document.getElementById('customSymbol') as HTMLInputElement
      if (customInput) {
        customInput.style.display = 'block'
        customInput.focus()
      }
          } else {
        const customInput = document.getElementById('customSymbol') as HTMLInputElement
        if (customInput) {
          customInput.style.display = 'none'
        }
        setSelectedSymbol(value)
        // 同时保存到localStorage
        localStorage.setItem('selected_symbol', value)
      }
  }

  const handleCustomSymbolChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value.toUpperCase()
    if (value) {
      setSelectedSymbol(value)
      // 同时保存到localStorage
      localStorage.setItem('selected_symbol', value)
    }
  }

  // 保存配置
  const handleSaveConfig = async () => {
    handleButtonClick()
    
    try {
      console.log('🔍 开始保存配置到数据库，使用state值:')
      console.log('formConfig:', formConfig)
      
      // 🔧 新的数据库格式配置
      const strategyConfigForDB = {
        symbol: selectedSymbol,
        isActive: false, // 保存时不激活
        
        // 策略参数
        parameters: {
          capitalRatio: formConfig.capitalRatio,
          initialCapital: formConfig.initialCapital,
          maxSingleTrade: formConfig.maxSingleTrade,
          scalpingWeight: formConfig.scalpingWeight,
          trendWeight: formConfig.trendWeight,
          gridWeight: formConfig.gridWeight,
          superTrendWeight: formConfig.superTrendWeight,
          gridSpacing: formConfig.gridSpacing,
          scalpProfit: formConfig.scalpProfit,
          trendFastProfit: formConfig.trendFastProfit
        },
        
        // 风控参数配置
        riskConfig: {
          maxDrawdownLimit: parseFloat((document.querySelector('input[data-field="maxDrawdownLimit"]') as HTMLInputElement)?.value || '35'),
          maxPositionRatio: parseFloat((document.querySelector('input[data-field="maxPositionRatio"]') as HTMLInputElement)?.value || '98'),
          profitProtectionLine: parseFloat((document.querySelector('input[data-field="profitProtectionLine"]') as HTMLInputElement)?.value || '15'),
          maxLeverageRisk: parseFloat((document.querySelector('input[data-field="maxLeverageRisk"]') as HTMLInputElement)?.value || '3.0')
        },
        
        // 运行时状态初始化
        runtime: {
          lastHeartbeat: Date.now(),
          currentStatus: 'inactive' as const,
          totalTrades: 0,
          realizedPnl: 0,
          unrealizedPnl: 0
        }
      }
      
      // 🔧 优先保存到数据库（注意：这里只保存配置，不激活）
      console.log('💾 保存策略配置到数据库...')
      
      // 同时保存兼容格式到localStorage
      const legacyConfig = {
        selectedSymbol,
        timestamp: Date.now(),
        
        // 资金配置
        fundConfig: {
          capitalRatio: formConfig.capitalRatio,
          initialCapital: formConfig.initialCapital,
          maxSingleTrade: formConfig.maxSingleTrade
        },
        
        // 策略权重配置
        strategyWeights: {
          scalping: formConfig.scalpingWeight,
          trend: formConfig.trendWeight,
          grid: formConfig.gridWeight,
          superTrend: formConfig.superTrendWeight
        },
        
        // 高级参数配置
        advancedParams: {
          gridSpacing: formConfig.gridSpacing,
          scalpProfit: formConfig.scalpProfit,
          trendFastProfit: formConfig.trendFastProfit,
          maxLeverage: 2.0,
          tradingHours: '24h',
          maxDrawdown: -35,
          profitProtection: strategyConfigForDB.riskConfig.profitProtectionLine,
          slippageTolerance: 0.02,
          rebalanceFreq: '1h'
        },
        
        riskConfig: strategyConfigForDB.riskConfig
      }
      
      localStorage.setItem('strategy_config', JSON.stringify(legacyConfig))
      localStorage.setItem('selected_symbol', selectedSymbol)
      
      setStatusMessage('✅ 所有配置已保存成功（数据库+localStorage）')
      setLastOperationTime(`保存时间: ${new Date().toLocaleTimeString()}`)
      
      // 3秒后清除消息
      setTimeout(() => setStatusMessage(''), 3000)
      
      console.log('✅ 策略配置已保存到数据库格式和localStorage兼容格式')
      console.log('📊 数据库格式:', strategyConfigForDB)
      console.log('💾 localStorage格式:', legacyConfig)
      
    } catch (error) {
      console.error('❌ 保存配置失败:', error)
      setStatusMessage('❌ 保存配置失败')
      setTimeout(() => setStatusMessage(''), 3000)
    }
  }

  // 加载保存的配置
  const loadSavedConfig = () => {
    try {
      const savedConfig = localStorage.getItem('strategy_config')
      if (savedConfig) {
        const config = JSON.parse(savedConfig)
        console.log('📥 加载保存的配置:', config)
        
        // 更新state而不是操作DOM
        if (config.fundConfig || config.strategyWeights || config.advancedParams) {
          setFormConfig(prevConfig => ({
            ...prevConfig,
            // 恢复资金配置
            ...(config.fundConfig && {
              capitalRatio: config.fundConfig.capitalRatio || prevConfig.capitalRatio,
              initialCapital: config.fundConfig.initialCapital || prevConfig.initialCapital,
              maxSingleTrade: config.fundConfig.maxSingleTrade || prevConfig.maxSingleTrade,
            }),
            // 恢复策略权重配置
            ...(config.strategyWeights && {
              scalpingWeight: config.strategyWeights.scalping || prevConfig.scalpingWeight,
              trendWeight: config.strategyWeights.trend || prevConfig.trendWeight,
              gridWeight: config.strategyWeights.grid || prevConfig.gridWeight,
              superTrendWeight: config.strategyWeights.superTrend || prevConfig.superTrendWeight,
            }),
            // 恢复高级参数配置
            ...(config.advancedParams && {
              gridSpacing: config.advancedParams.gridSpacing || prevConfig.gridSpacing,
              scalpProfit: config.advancedParams.scalpProfit || prevConfig.scalpProfit,
              trendFastProfit: config.advancedParams.trendFastProfit || prevConfig.trendFastProfit,
            })
          }))
          
          console.log('✅ 配置恢复到state完成')
        }
      }
    } catch (error) {
      console.error('❌ 加载配置失败:', error)
    }
  }

  // 重置为推荐配置
  const handleResetConfig = () => {
    handleButtonClick()
    
    try {
      // 重置为默认推荐配置
      setSelectedSymbol('BTCUSDT')
      localStorage.setItem('selected_symbol', 'BTCUSDT')
      
      // 清除保存的配置
      localStorage.removeItem('strategy_config')
      
      // 重置state为默认值
      setFormConfig({
        capitalRatio: 25,
        initialCapital: 10000,
        maxSingleTrade: 2500,
        scalpingWeight: 50.6,
        trendWeight: 39.2,
        gridWeight: 9.5,
        superTrendWeight: 0.7,
        gridSpacing: 0.4,
        scalpProfit: 0.08,
        trendFastProfit: 0.2
      })
      
      setStatusMessage('✅ 已重置为推荐配置')
      setLastOperationTime(`重置时间: ${new Date().toLocaleTimeString()}`)
      
      // 3秒后清除消息
      setTimeout(() => setStatusMessage(''), 3000)
      
      console.log('✅ 策略配置已重置为默认值')
    } catch (error) {
      console.error('❌ 重置配置失败:', error)
      setStatusMessage('❌ 重置配置失败')
      setTimeout(() => setStatusMessage(''), 3000)
    }
  }

  // 系统启动/停止逻辑
  const handleSystemToggle = async () => {
    // 添加点击反馈
    handleButtonClick()
    
    // 详细的前置检查
    console.log('🔍 开始交易前置检查...')
    
    // 1. 检查API连接状态
    if (!liveMetrics.isConnected) {
      const errorMsg = '❌ 币安API未连接，请先配置API凭证并连接'
      console.error(errorMsg)
      setStatusMessage(errorMsg)
      setTimeout(() => setStatusMessage(''), 5000)
      
      // 引导用户到设置页面
      if (confirm('API未连接，是否跳转到系统设置页面配置API凭证？')) {
        window.location.href = '#/settings'
      }
      return
    }
    
    // 2. 检查币种选择
    if (!selectedSymbol || selectedSymbol.trim() === '') {
      const errorMsg = '❌ 请先选择交易币种'
      console.error(errorMsg)
      setStatusMessage(errorMsg)
      setTimeout(() => setStatusMessage(''), 3000)
      return
    }
    
    // 3. 检查策略执行器状态
    if (!strategyExecutor) {
      const errorMsg = '❌ 策略执行器未初始化，请刷新页面重试'
      console.error(errorMsg)
      setStatusMessage(errorMsg)
      setTimeout(() => setStatusMessage(''), 5000)
      return
    }
    
    console.log('✅ 前置检查通过，开始执行交易操作')
    
    // 设置加载状态
    setIsLoading(true)
    const currentTime = new Date().toLocaleTimeString()
    
    try {
      if (systemRunning) {
        // 停止策略终极版
        console.log('🛑 开始停止策略终极版...')
        setStatusMessage('🔄 正在停止策略终极版...')
        
        // 停止策略执行器
        try {
          strategyExecutor.stopStrategy()
          console.log('✅ 策略执行器已停止')
        } catch (error) {
          console.warn('⚠️ 策略执行器停止时出现警告:', error)
        }
        
        // 停止binance API
        try {
          await binanceApi.stopTrading()
          console.log('✅ Binance API已停止')
        } catch (error) {
          console.warn('⚠️ Binance API停止时出现警告:', error)
        }
        
        setSystemRunning(false)
        
        // 🔧 将状态更新到数据库而不是localStorage
        console.log('💾 更新策略状态到数据库 - 停止')
        await strategyStateService.stopAllStrategies()
        
        // 向后兼容：同时保持localStorage
        localStorage.setItem('trading_status', 'inactive')
        
        // 触发storage事件，通知实时监控页面停止
        window.dispatchEvent(new StorageEvent('storage', {
          key: 'trading_status',
          newValue: 'inactive',
          url: window.location.href
        }))
        
        setStatusMessage('✅ 策略终极版已成功停止')
        setLastOperationTime(`停止时间: ${currentTime}`)
        setStrategyStatus(null)
        console.log('✅ 策略终极版完全停止，实时监控已关闭')
        
      } else {
        // 启动策略终极版
        console.log('🚀 开始启动策略终极版...')
        setStatusMessage('🚀 正在启动策略终极版...')
        
        console.log('🎯 启动策略终极版配置:')
        console.log(`📈 目标币种: ${selectedSymbol}`)
        console.log(`📈 目标收益: ${STRATEGY_ULTIMATE_CONFIG.strategy_weights.scalping * 100}%超短线 + ${STRATEGY_ULTIMATE_CONFIG.strategy_weights.trend * 100}%趋势`)
        console.log(`🛡️ 风险控制: 最大回撤≤${STRATEGY_ULTIMATE_CONFIG.max_drawdown_limit * 100}%, 动态杠杆≤${STRATEGY_ULTIMATE_CONFIG.leverage_factor}x`)
        console.log(`⚡ 策略参数: 网格${STRATEGY_ULTIMATE_CONFIG.grid_spacing * 100}%, 超短线${STRATEGY_ULTIMATE_CONFIG.scalp_profit * 100}%`)
        
        // 启动策略执行器
        console.log('⚡ 启动策略执行器...')
        setStatusMessage('⚡ 启动策略执行器...')
        
        let strategySuccess = false
        try {
          strategySuccess = await strategyExecutor.startStrategy(selectedSymbol)
          console.log('策略执行器启动结果:', strategySuccess)
        } catch (error) {
          console.error('❌ 策略执行器启动异常:', error)
          throw new Error(`策略执行器启动失败: ${error instanceof Error ? error.message : '未知错误'}`)
        }
        
        if (!strategySuccess) {
          throw new Error('策略执行器启动失败，请检查账户连接状态和网络连接')
        }
        console.log('✅ 策略执行器启动成功')
        
        // 启动binance API
        console.log('📡 启动Binance API...')
        setStatusMessage('📡 启动Binance API...')
        
        try {
          await binanceApi.startTrading()
          console.log('✅ Binance API启动成功')
        } catch (error) {
          console.error('❌ Binance API启动失败:', error)
          // 如果API启动失败，也要停止策略执行器
          strategyExecutor.stopStrategy()
          throw new Error(`Binance API启动失败: ${error instanceof Error ? error.message : '网络连接问题'}`)
        }
        
        setSystemRunning(true)
        
        // 🔧 保存策略配置并激活到数据库
        console.log('💾 准备保存策略配置到数据库...')
        
        try {
          const strategyConfigForDB = {
            symbol: selectedSymbol,
            isActive: true,
            parameters: {
              capitalRatio: formConfig.capitalRatio,
              initialCapital: formConfig.initialCapital,
              maxSingleTrade: formConfig.maxSingleTrade,
              scalpingWeight: formConfig.scalpingWeight,
              trendWeight: formConfig.trendWeight,
              gridWeight: formConfig.gridWeight,
              superTrendWeight: formConfig.superTrendWeight,
              gridSpacing: formConfig.gridSpacing,
              scalpProfit: formConfig.scalpProfit,
              trendFastProfit: formConfig.trendFastProfit
            },
            riskConfig: {
              maxDrawdownLimit: 35,
              maxPositionRatio: 98,
              profitProtectionLine: 15,
              maxLeverageRisk: 3.0
            },
            runtime: {
              lastHeartbeat: Date.now(),
              currentStatus: 'active' as const,
              totalTrades: 0,
              realizedPnl: 0,
              unrealizedPnl: 0
            }
          }
          
          console.log('📋 策略配置详情:', strategyConfigForDB)
          
          // 保存策略配置到数据库
          setStatusMessage('💾 保存策略配置到数据库...')
          const dbSaveSuccess = await strategyStateService.saveAndActivateStrategy(strategyConfigForDB)
          
          if (!dbSaveSuccess) {
            console.error('❌ 数据库保存失败，但继续执行')
            setStatusMessage('⚠️ 数据库保存失败，使用兼容模式...')
          } else {
            console.log('✅ 策略配置已成功保存到数据库')
            setStatusMessage('✅ 策略配置已保存到数据库')
          }
          
        } catch (dbError) {
          console.error('❌ 数据库操作失败:', dbError)
          setStatusMessage('⚠️ 数据库操作失败，使用兼容模式...')
          // 继续执行，不因数据库问题阻止交易启动
        }
        
        // 向后兼容：同时保持localStorage
        localStorage.setItem('trading_status', 'active')
        localStorage.setItem('selected_symbol', selectedSymbol)
        
        // 触发storage事件，通知实时监控页面
        window.dispatchEvent(new StorageEvent('storage', {
          key: 'trading_status',
          newValue: 'active',
          url: window.location.href
        }))
        
        setStatusMessage(`✅ 策略终极版启动成功 - 监控币种: ${selectedSymbol}`)
        setLastOperationTime(`启动时间: ${currentTime}`)
        console.log(`✅ 策略终极版完全启动，开始执行智能交易 ${selectedSymbol}`)
        
        // 获取初始策略状态
        try {
          const initialStatus = strategyExecutor.getStrategyStatus()
          setStrategyStatus(initialStatus)
          console.log('✅ 策略状态获取成功:', initialStatus)
        } catch (error) {
          console.warn('⚠️ 策略状态获取失败:', error)
        }
      }
      
      // 更新实时数据
      try {
        await updateLiveData()
        console.log('✅ 实时数据更新成功')
      } catch (error) {
        console.warn('⚠️ 实时数据更新失败:', error)
      }
      
      // 3秒后清除成功消息
      setTimeout(() => setStatusMessage(''), 3000)
      
    } catch (error) {
      console.error('❌ 交易操作失败:', error)
      
      // 详细的错误信息
      let errorMessage = '未知错误'
      if (error instanceof Error) {
        errorMessage = error.message
        
        // 根据错误类型提供解决建议
        if (errorMessage.includes('网络') || errorMessage.includes('连接')) {
          errorMessage += '\n🔧 建议: 检查网络连接，如在国内请启用代理'
        } else if (errorMessage.includes('API') || errorMessage.includes('凭证')) {
          errorMessage += '\n🔧 建议: 请检查系统设置中的API配置'
        } else if (errorMessage.includes('账户') || errorMessage.includes('余额')) {
          errorMessage += '\n🔧 建议: 请检查账户余额和权限设置'
        }
      }
      
      setStatusMessage(`❌ 操作失败: ${errorMessage}`)
      setTimeout(() => setStatusMessage(''), 8000)
      
      // 确保失败时重置状态
      setSystemRunning(false)
      localStorage.setItem('trading_status', 'inactive')
      
    } finally {
      setIsLoading(false)
    }
  }



  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">策略配置</h1>
          <p className="text-slate-400 mt-1">
            智能策略终极版 - 实盘交易监控
          </p>
        </div>
      </div>

      {/* 系统主控制面板 */}
      <Card className="bg-gradient-to-r from-slate-800 to-slate-700 border-slate-600 shadow-lg">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className={`w-4 h-4 rounded-full ${systemRunning ? 'bg-green-500 animate-pulse' : 'bg-red-500'}`}></div>
              <div>
                <h2 className="text-xl font-bold text-white">实盘交易状态</h2>
                <p className="text-slate-400">
                  {liveMetrics.isConnected ? 
                    (systemRunning ? '策略终极版正在实盘交易' : '已连接币安，等待启动') : 
                    '未连接币安API，请先连接'
                  }
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className="text-right mr-4">
                <p className="text-sm text-slate-400">连接状态</p>
                <p className={`font-semibold ${
                  liveMetrics.isConnected ? 'text-green-400' : 'text-red-400'
                }`}>
                  {liveMetrics.isConnected ? (systemRunning ? '交易中' : '已连接') : '未连接'}
                </p>
              </div>
              
              {!liveMetrics.isConnected ? (
                <Button 
                  size="lg"
                  className={`bg-blue-600 hover:bg-blue-700 text-white min-w-[120px] transition-all duration-150 ${
                    buttonClicked ? 'scale-95 bg-blue-500' : ''
                  }`}
                  onClick={checkInitialConnection}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <div className="w-5 h-5 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      连接中...
                    </>
                  ) : (
                    <>
                      <Target className="w-5 h-5 mr-2" />
                      重新连接
                    </>
                  )}
                </Button>
              ) : systemRunning ? (
                <Button 
                  size="lg"
                  className={`bg-red-600 hover:bg-red-700 text-white min-w-[120px] relative transition-all duration-150 ${
                    buttonClicked ? 'scale-95 bg-red-500' : ''
                  }`}
                  onClick={handleSystemToggle}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <div className="w-5 h-5 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      停止中...
                    </>
                  ) : (
                    <>
                      <Pause className="w-5 h-5 mr-2" />
                      停止交易
                    </>
                  )}
                </Button>
              ) : (
                <Button 
                  size="lg"
                  className={`bg-green-600 hover:bg-green-700 text-white min-w-[120px] relative transition-all duration-150 ${
                    buttonClicked ? 'scale-95 bg-green-500' : ''
                  }`}
                  onClick={handleSystemToggle}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <div className="w-5 h-5 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      启动中...
                    </>
                  ) : (
                    <>
                      <Play className="w-5 h-5 mr-2" />
                      开始交易
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>
          
          {/* 状态消息显示 */}
          {(statusMessage || lastOperationTime) && (
            <div className="mt-4 p-3 bg-slate-700/50 border border-slate-600/50 rounded-lg">
              {statusMessage && (
                <div className={`text-sm font-medium mb-1 ${
                  statusMessage.includes('❌') ? 'text-red-400' :
                  statusMessage.includes('✅') ? 'text-green-400' :
                  statusMessage.includes('🔄') || statusMessage.includes('🚀') ? 'text-blue-400' :
                  'text-white'
                }`}>
                  {statusMessage}
                </div>
              )}
              {lastOperationTime && (
                <div className="text-xs text-slate-400">
                  {lastOperationTime}
                </div>
              )}
            </div>
          )}

          {/* 🔧 新增：手动价格输入 (降级模式) */}
          {systemRunning && (
            <div className="mt-4 p-4 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
              <div className="flex items-center gap-2 mb-3">
                <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
                <h4 className="text-yellow-300 font-medium">手动价格更新</h4>
                <span className="text-xs text-yellow-300 bg-yellow-500/20 px-2 py-1 rounded">降级模式</span>
              </div>
              <div className="flex items-center gap-3">
                <input
                  type="number"
                  step="0.01"
                  placeholder={`输入${selectedSymbol}当前价格`}
                  className="flex-1 p-2 bg-slate-700 border border-slate-600 rounded text-white text-sm"
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      const price = parseFloat((e.target as HTMLInputElement).value)
                      if (price > 0) {
                        // 调用策略执行器的手动价格更新方法
                        const success = strategyExecutor.updateManualPrice(price)
                        if (success) {
                          setStatusMessage(`✅ 价格已更新: ${price.toFixed(2)}`)
                          ;(e.target as HTMLInputElement).value = ''
                        } else {
                          setStatusMessage('❌ 价格更新失败，请检查输入')
                        }
                      }
                    }
                  }}
                />
                <Button
                  size="sm"
                  className="bg-yellow-600 hover:bg-yellow-700 text-white"
                  onClick={() => {
                    const input = document.querySelector('input[placeholder*="当前价格"]') as HTMLInputElement
                    if (input) {
                      const price = parseFloat(input.value)
                      if (price > 0) {
                        const success = strategyExecutor.updateManualPrice(price)
                        if (success) {
                          setStatusMessage(`✅ 价格已更新: ${price.toFixed(2)}`)
                          input.value = ''
                        } else {
                          setStatusMessage('❌ 价格更新失败，请检查输入')
                        }
                      }
                    }
                  }}
                >
                  更新价格
                </Button>
              </div>
              <p className="text-xs text-yellow-300 mt-2">
                💡 当WebSocket连接失败时，您可以手动输入真实价格来保持策略运行
              </p>
            </div>
          )}

          {/* 系统状态详情 */}
          <div className="mt-4 pt-4 border-t border-slate-600/50">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4 text-slate-400" />
                <span className="text-slate-400">交易时间:</span>
                <span className="text-white font-medium">实时交易</span>
              </div>
              <div className="flex items-center gap-2">
                <Target className="w-4 h-4 text-slate-400" />
                <span className="text-slate-400">连接状态:</span>
                <span className="text-white font-medium">
                  {liveMetrics.isConnected ? '已连接' : '未连接'}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <BarChart3 className="w-4 h-4 text-slate-400" />
                <span className="text-slate-400">今日交易:</span>
                <span className="text-white font-medium">{liveMetrics.todayTrades.toLocaleString()}笔</span>
              </div>
              <div className="flex items-center gap-2">
                <Shield className="w-4 h-4 text-slate-400" />
                <span className="text-slate-400">当前回撤:</span>
                <span className="text-yellow-400 font-medium">
                  -{liveMetrics.currentDrawdown.toFixed(2)}%
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">策略总览</TabsTrigger>
          <TabsTrigger value="config">策略配置</TabsTrigger>
          <TabsTrigger value="monitor">策略监控</TabsTrigger>
          <TabsTrigger value="risk">风控设置</TabsTrigger>
        </TabsList>

        {/* 策略总览 */}
        <TabsContent value="overview" className="space-y-6">


          {/* 智能策略终极版信息卡片 */}
          <Card className="bg-gradient-to-r from-blue-900/30 to-purple-900/30 border-blue-500/30">
            <CardHeader>
              <CardTitle className="text-white flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center">
                    <Zap className="w-5 h-5 text-blue-400" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold">智能策略终极版</h3>
                    <p className="text-sm text-slate-400">超高频多策略融合系统 - 专为币安实盘设计</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                    liveMetrics.isConnected ? 
                      (systemRunning ? 'bg-green-500/20 text-green-400' : 'bg-yellow-500/20 text-yellow-400') :
                      'bg-red-500/20 text-red-400'
                  }`}>
                    {liveMetrics.isConnected ? 
                      (systemRunning ? '运行中' : '等待启动') : 
                      '未连接'
                    }
                  </div>
                  <Switch checked={systemRunning} disabled />
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                {/* 历史表现指标 */}
                <div className="text-center p-4 bg-green-500/10 border border-green-500/30 rounded-lg">
                  <p className="text-green-300 text-sm mb-1">年化收益率</p>
                  <p className="text-2xl font-bold text-green-400">+450.79%</p>
                  <p className="text-xs text-green-300 mt-1">回测期间</p>
                </div>
                <div className="text-center p-4 bg-orange-500/10 border border-orange-500/30 rounded-lg">
                  <p className="text-orange-300 text-sm mb-1">最大回撤</p>
                  <p className="text-2xl font-bold text-orange-400">-33.24%</p>
                  <p className="text-xs text-orange-300 mt-1">风险可控</p>
                </div>
                <div className="text-center p-4 bg-blue-500/10 border border-blue-500/30 rounded-lg">
                  <p className="text-blue-300 text-sm mb-1">历史交易</p>
                  <p className="text-2xl font-bold text-blue-400">66,312</p>
                  <p className="text-xs text-blue-300 mt-1">超高频</p>
                </div>
                <div className="text-center p-4 bg-purple-500/10 border border-purple-500/30 rounded-lg">
                  <p className="text-purple-300 text-sm mb-1">平均杠杆</p>
                  <p className="text-2xl font-bold text-purple-400">1.27x</p>
                  <p className="text-xs text-purple-300 mt-1">温和使用</p>
                </div>
              </div>

              {/* 策略详情 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <h4 className="text-white font-semibold">策略基本信息</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-slate-400">策略类型:</span>
                      <span className="text-white">多策略融合系统</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-400">交易标的:</span>
                      <span className="text-white">{selectedSymbol}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-400">初始资金:</span>
                      <span className="text-white">$100,000</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-400">最终资产:</span>
                      <span className="text-green-400">$1,026,783,830</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-400">数据源:</span>
                      <span className="text-white">15分钟K线</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-400">月化收益:</span>
                      <span className="text-green-400">15.05%</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <h4 className="text-white font-semibold">实盘状态</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-slate-400">连接状态:</span>
                      <span className={liveMetrics.isConnected ? 'text-green-400' : 'text-red-400'}>
                        {liveMetrics.isConnected ? '已连接币安' : '未连接API'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-400">交易状态:</span>
                      <span className={systemRunning ? 'text-green-400' : 'text-yellow-400'}>
                        {systemRunning ? '多策略运行中' : '等待启动'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-400">当前持仓:</span>
                      <span className="text-white">${liveMetrics.currentPosition.toFixed(4)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-400">未实现盈亏:</span>
                      <span className={liveMetrics.unrealizedPnL >= 0 ? 'text-green-400' : 'text-red-400'}>
                        {liveMetrics.unrealizedPnL >= 0 ? '+' : ''}${liveMetrics.unrealizedPnL.toFixed(2)}
                      </span>
                    </div>
                    {strategyStatus && (
                      <>
                        <div className="flex justify-between">
                          <span className="text-slate-400">策略交易:</span>
                          <span className="text-blue-400">{strategyStatus.totalTrades}笔</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-slate-400">策略胜率:</span>
                          <span className="text-green-400">{strategyStatus.winRate.toFixed(1)}%</span>
                        </div>
                      </>
                    )}
                    <div className="flex justify-between">
                      <span className="text-slate-400">风险等级:</span>
                      <span className={`${
                        liveMetrics.riskLevel === 'LOW' ? 'text-green-400' : 
                        liveMetrics.riskLevel === 'MEDIUM' ? 'text-yellow-400' : 'text-red-400'
                      }`}>
                        {liveMetrics.riskLevel === 'LOW' ? '低' : 
                         liveMetrics.riskLevel === 'MEDIUM' ? '中' : '高'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-400">最后更新:</span>
                      <span className="text-white">{new Date().toLocaleTimeString()}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* 策略组成说明 */}
              <div className="mt-6 p-4 bg-blue-500/10 border border-blue-500/30 rounded-lg">
                <h4 className="text-blue-300 font-semibold mb-2">🎯 多策略融合架构</h4>
                <div className="text-sm text-blue-200 space-y-1">
                  <p>• <strong>超短线策略</strong>: 33,528次交易 (50.6%) - 主力盈利策略</p>
                  <p>• <strong>趋势策略</strong>: 25,977次交易 (39.2%) - 核心跟踪策略</p>
                  <p>• <strong>网格策略</strong>: 6,319次交易 (9.5%) - 稳定基础策略</p>
                  <p>• <strong>超强趋势</strong>: 488次交易 (0.7%) - 爆发利器策略</p>
                </div>
              </div>

              {/* 技术特色 */}
              <div className="mt-4 p-4 bg-purple-500/10 border border-purple-500/30 rounded-lg">
                <h4 className="text-purple-300 font-semibold mb-2">⚡ 核心技术特色</h4>
                <div className="text-sm text-purple-200 space-y-1">
                  <p>• <strong>动态杠杆</strong>: 1.27倍平均杠杆，最高2.64倍，智能风控</p>
                  <p>• <strong>多时间框架</strong>: 15分钟到4小时多周期技术分析</p>
                  <p>• <strong>市场状态识别</strong>: 6种市场状态智能切换策略</p>
                  <p>• <strong>超高频执行</strong>: 毫秒级订单执行，精确入场离场</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 策略配置 */}
        <TabsContent value="config" className="space-y-6">
          {/* 推荐配置展示 */}
          <Card className="bg-gradient-to-r from-blue-900/30 to-green-900/30 border-blue-500/30">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-3">
                <div className="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center">
                  <Target className="w-5 h-5 text-blue-400" />
                </div>
                <div>
                  <h3 className="text-xl font-bold">智能推荐配置</h3>
                  <p className="text-sm text-slate-400">基于历史表现优化的最佳参数组合</p>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                {/* 保守型配置 */}
                <div className="p-4 bg-green-500/10 border border-green-500/30 rounded-lg">
                  <div className="flex items-center gap-2 mb-3">
                    <Shield className="w-5 h-5 text-green-400" />
                    <h4 className="text-green-300 font-semibold">保守型</h4>
                    <span className="text-xs text-green-300 bg-green-500/20 px-2 py-1 rounded">推荐新手</span>
                  </div>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-slate-400">资金配置:</span>
                      <span className="text-white">10-20%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-400">最大杠杆:</span>
                      <span className="text-white">1.5x</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-400">回撤限制:</span>
                      <span className="text-white">-20%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-400">预期收益:</span>
                      <span className="text-green-400">200-300%</span>
                    </div>
                  </div>
                  <Button className="w-full mt-3 bg-green-600 hover:bg-green-700" size="sm">
                    应用保守配置
                  </Button>
                </div>

                {/* 均衡型配置 */}
                <div className="p-4 bg-blue-500/10 border border-blue-500/30 rounded-lg relative">
                  <div className="absolute -top-2 -right-2 bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
                    推荐
                  </div>
                  <div className="flex items-center gap-2 mb-3">
                    <BarChart3 className="w-5 h-5 text-blue-400" />
                    <h4 className="text-blue-300 font-semibold">均衡型</h4>
                    <span className="text-xs text-blue-300 bg-blue-500/20 px-2 py-1 rounded">最佳平衡</span>
                  </div>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-slate-400">资金配置:</span>
                      <span className="text-white">20-40%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-400">最大杠杆:</span>
                      <span className="text-white">2.5x</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-400">回撤限制:</span>
                      <span className="text-white">-30%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-400">预期收益:</span>
                      <span className="text-blue-400">350-450%</span>
                    </div>
                  </div>
                  <Button className="w-full mt-3 bg-blue-600 hover:bg-blue-700" size="sm">
                    应用均衡配置
                  </Button>
                </div>

                {/* 激进型配置 */}
                <div className="p-4 bg-red-500/10 border border-red-500/30 rounded-lg">
                  <div className="flex items-center gap-2 mb-3">
                    <Zap className="w-5 h-5 text-red-400" />
                    <h4 className="text-red-300 font-semibold">激进型</h4>
                    <span className="text-xs text-red-300 bg-red-500/20 px-2 py-1 rounded">高风险</span>
                  </div>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-slate-400">资金配置:</span>
                      <span className="text-white">40-60%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-400">最大杠杆:</span>
                      <span className="text-white">3.0x</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-400">回撤限制:</span>
                      <span className="text-white">-40%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-400">预期收益:</span>
                      <span className="text-red-400">450%+</span>
                    </div>
                  </div>
                  <Button className="w-full mt-3 bg-red-600 hover:bg-red-700" size="sm">
                    应用激进配置
                  </Button>
                </div>
              </div>

              <div className="p-4 bg-purple-500/10 border border-purple-500/30 rounded-lg">
                <h4 className="text-purple-300 font-semibold mb-2">💡 配置建议</h4>
                <div className="text-sm text-purple-200 space-y-1">
                  <p>• <strong>新手用户</strong>: 建议从保守型开始，熟悉系统后逐步调整</p>
                  <p>• <strong>经验用户</strong>: 均衡型配置在风险和收益间取得最佳平衡</p>
                  <p>• <strong>专业用户</strong>: 可选择激进型或自定义配置追求更高收益</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 手动配置参数 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 资金配置 */}
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <Settings className="w-5 h-5" />
                  资金配置
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-slate-300 mb-2 block">
                    操作币种
                  </label>
                  <div className="flex gap-3">
                    <select 
                      className="flex-1 p-3 bg-slate-700 border border-slate-600 rounded-lg text-white"
                      onChange={handleSymbolChange}
                      value={selectedSymbol}
                    >
                      <option value="BTCUSDT">BTCUSDT (比特币)</option>
                      <option value="ETHUSDT">ETHUSDT (以太坊)</option>
                      <option value="BNBUSDT">BNBUSDT (币安币)</option>
                      <option value="ADAUSDT">ADAUSDT (艾达币)</option>
                      <option value="SOLUSDT">SOLUSDT (索拉纳)</option>
                      <option value="DOTUSDT">DOTUSDT (波卡)</option>
                      <option value="AVAXUSDT">AVAXUSDT (雪崩)</option>
                      <option value="MATICUSDT">MATICUSDT (马蹄)</option>
                      <option value="LINKUSDT">LINKUSDT (链克)</option>
                      <option value="UNIUSDT">UNIUSDT (Uniswap)</option>
                      <option value="custom">自定义币种</option>
                    </select>
                    <input 
                      type="text" 
                      className="w-32 p-3 bg-slate-700 border border-slate-600 rounded-lg text-white" 
                      placeholder="如DOGEUSDT"
                      style={{display: 'none'}}
                      id="customSymbol"
                      onChange={handleCustomSymbolChange}
                      onBlur={(e) => {
                        if (!e.target.value) {
                          e.target.style.display = 'none'
                          setSelectedSymbol('BTCUSDT')
                        }
                      }}
                    />
                  </div>
                  <p className="text-xs text-slate-400 mt-1">选择要交易的数字货币对，系统将基于此币种执行策略</p>
                </div>

                <div>
                  <label className="text-sm font-medium text-slate-300 mb-2 block">
                    资金投入比例 (%)
                  </label>
                  <div className="flex items-center gap-3">
                    <input 
                      type="range"
                      min="5" 
                      max="100" 
                      value={formConfig.capitalRatio}
                      onChange={(e) => setFormConfig(prev => ({ ...prev, capitalRatio: parseFloat(e.target.value) }))}
                      className="flex-1 h-2 bg-slate-700 rounded-lg appearance-none cursor-pointer"
                    />
                    <span className="text-white font-medium min-w-[60px]">{formConfig.capitalRatio}%</span>
                  </div>
                  <p className="text-xs text-slate-400 mt-1">当前账户资金的投入比例</p>
                </div>

                <div>
                  <label className="text-sm font-medium text-slate-300 mb-2 block">
                    初始投入资金 (USDT)
                  </label>
                  <input 
                    type="number" 
                    className="w-full p-3 bg-slate-700 border border-slate-600 rounded-lg text-white"
                    placeholder="输入初始资金"
                    value={formConfig.initialCapital}
                    onChange={(e) => setFormConfig(prev => ({ ...prev, initialCapital: parseFloat(e.target.value) || 0 }))}
                  />
                  <p className="text-xs text-slate-400 mt-1">建议最少投入1000 USDT以确保策略效果</p>
                </div>

                <div>
                  <label className="text-sm font-medium text-slate-300 mb-2 block">
                    单笔最大投入 (USDT)
                  </label>
                  <input 
                    type="number" 
                    className="w-full p-3 bg-slate-700 border border-slate-600 rounded-lg text-white"
                    placeholder="单笔最大金额"
                    value={formConfig.maxSingleTrade}
                    onChange={(e) => setFormConfig(prev => ({ ...prev, maxSingleTrade: parseFloat(e.target.value) || 0 }))}
                  />
                  <p className="text-xs text-slate-400 mt-1">控制单次交易的最大资金量</p>
                </div>
              </CardContent>
            </Card>

            {/* 策略权重配置 */}
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <BarChart3 className="w-5 h-5" />
                  策略权重配置
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-slate-300 mb-2 flex items-center justify-between">
                    <span>超短线策略</span>
                    <span className="text-blue-400">{formConfig.scalpingWeight}%</span>
                  </label>
                  <input 
                    type="range"
                    min="40" 
                    max="60" 
                    step="0.1"
                    value={formConfig.scalpingWeight}
                    onChange={(e) => setFormConfig(prev => ({ ...prev, scalpingWeight: parseFloat(e.target.value) }))}
                    className="w-full h-2 bg-slate-700 rounded-lg appearance-none cursor-pointer"
                  />
                  <p className="text-xs text-slate-400 mt-1">主力盈利策略，33,528次交易，止盈0.08%</p>
                </div>

                <div>
                  <label className="text-sm font-medium text-slate-300 mb-2 flex items-center justify-between">
                    <span>趋势跟踪策略</span>
                    <span className="text-purple-400">{formConfig.trendWeight}%</span>
                  </label>
                  <input 
                    type="range"
                    min="30" 
                    max="50" 
                    step="0.1"
                    value={formConfig.trendWeight}
                    onChange={(e) => setFormConfig(prev => ({ ...prev, trendWeight: parseFloat(e.target.value) }))}
                    className="w-full h-2 bg-slate-700 rounded-lg appearance-none cursor-pointer"
                  />
                  <p className="text-xs text-slate-400 mt-1">核心策略，25,977次交易，双档止盈0.2%/0.8%</p>
                </div>

                <div>
                  <label className="text-sm font-medium text-slate-300 mb-2 flex items-center justify-between">
                    <span>网格策略</span>
                    <span className="text-orange-400">{formConfig.gridWeight}%</span>
                  </label>
                  <input 
                    type="range"
                    min="5" 
                    max="15" 
                    step="0.1"
                    value={formConfig.gridWeight}
                    onChange={(e) => setFormConfig(prev => ({ ...prev, gridWeight: parseFloat(e.target.value) }))}
                    className="w-full h-2 bg-slate-700 rounded-lg appearance-none cursor-pointer"
                  />
                  <p className="text-xs text-slate-400 mt-1">稳定基础策略，6,319次交易，间距0.4%</p>
                </div>

                <div>
                  <label className="text-sm font-medium text-slate-300 mb-2 flex items-center justify-between">
                    <span>超强趋势策略</span>
                    <span className="text-red-400">{formConfig.superTrendWeight}%</span>
                  </label>
                  <input 
                    type="range"
                    min="0.5" 
                    max="2" 
                    step="0.1"
                    value={formConfig.superTrendWeight}
                    onChange={(e) => setFormConfig(prev => ({ ...prev, superTrendWeight: parseFloat(e.target.value) }))}
                    className="w-full h-2 bg-slate-700 rounded-lg appearance-none cursor-pointer"
                  />
                  <p className="text-xs text-slate-400 mt-1">爆发利器，488次交易，阈值2.0%</p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 高级参数配置 */}
          <Card className="bg-slate-800/50 border-slate-700/50">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Zap className="w-5 h-5" />
                高级参数配置
              </CardTitle>
            </CardHeader>
            <CardContent>
                             <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                 <div>
                   <label className="text-sm font-medium text-slate-300 mb-2 block">
                     网格间距 (%)
                   </label>
                   <input 
                     type="number"
                     step="0.01"
                     min="0.1"
                     max="1"
                     className="w-full p-3 bg-slate-700 border border-slate-600 rounded-lg text-white"
                     value={formConfig.gridSpacing}
                     onChange={(e) => setFormConfig(prev => ({ ...prev, gridSpacing: parseFloat(e.target.value) || 0 }))}
                   />
                   <p className="text-xs text-slate-400 mt-1">网格策略基础间距，动态调整</p>
                 </div>

                 <div>
                   <label className="text-sm font-medium text-slate-300 mb-2 block">
                     超短线止盈 (%)
                   </label>
                   <input 
                     type="number"
                     step="0.01"
                     min="0.05"
                     max="0.2"
                     className="w-full p-3 bg-slate-700 border border-slate-600 rounded-lg text-white"
                     value={formConfig.scalpProfit}
                     onChange={(e) => setFormConfig(prev => ({ ...prev, scalpProfit: parseFloat(e.target.value) || 0 }))}
                   />
                   <p className="text-xs text-slate-400 mt-1">超短线策略止盈目标</p>
                 </div>

                 <div>
                   <label className="text-sm font-medium text-slate-300 mb-2 block">
                     趋势快速止盈 (%)
                   </label>
                   <input 
                     type="number"
                     step="0.01"
                     min="0.1"
                     max="0.5"
                     className="w-full p-3 bg-slate-700 border border-slate-600 rounded-lg text-white"
                     value={formConfig.trendFastProfit}
                     onChange={(e) => setFormConfig(prev => ({ ...prev, trendFastProfit: parseFloat(e.target.value) || 0 }))}
                   />
                   <p className="text-xs text-slate-400 mt-1">趋势策略第一档止盈</p>
                 </div>
                                 <div>
                   <label className="text-sm font-medium text-slate-300 mb-2 block">
                     最大杠杆倍数
                   </label>
                   <select className="w-full p-3 bg-slate-700 border border-slate-600 rounded-lg text-white">
                     <option value="1.5">1.5x (保守)</option>
                     <option value="2.0" selected>2.0x (标准)</option>
                     <option value="2.5">2.5x (积极)</option>
                     <option value="3.0">3.0x (最大限制)</option>
                   </select>
                   <p className="text-xs text-slate-400 mt-1">历史平均1.27x，最高2.64x</p>
                 </div>

                <div>
                  <label className="text-sm font-medium text-slate-300 mb-2 block">
                    交易时间段
                  </label>
                  <select className="w-full p-3 bg-slate-700 border border-slate-600 rounded-lg text-white">
                    <option value="24h" selected>24小时交易</option>
                    <option value="asia">亚洲时段 (00:00-12:00 UTC)</option>
                    <option value="europe">欧洲时段 (06:00-18:00 UTC)</option>
                    <option value="america">美洲时段 (12:00-24:00 UTC)</option>
                    <option value="custom">自定义时段</option>
                  </select>
                </div>

                                 <div>
                   <label className="text-sm font-medium text-slate-300 mb-2 block">
                     最大回撤限制
                   </label>
                   <select className="w-full p-3 bg-slate-700 border border-slate-600 rounded-lg text-white">
                     <option value="-20">-20% (保守)</option>
                     <option value="-25">-25% (稳健)</option>
                     <option value="-30">-30% (标准)</option>
                     <option value="-35" selected>-35% (系统默认)</option>
                     <option value="-40">-40% (激进)</option>
                   </select>
                   <p className="text-xs text-slate-400 mt-1">历史最大回撤: -33.24%</p>
                 </div>

                <div>
                  <label className="text-sm font-medium text-slate-300 mb-2 block">
                    利润回撤保护 (%)
                  </label>
                  <input 
                    data-field="profitProtection"
                    type="number"
                    step="1"
                    min="5"
                    max="50"
                    className="w-full p-3 bg-slate-700 border border-slate-600 rounded-lg text-white"
                    defaultValue="15"
                  />
                  <p className="text-xs text-slate-400 mt-1">盈利后的保护线</p>
                </div>

                                 <div>
                   <label className="text-sm font-medium text-slate-300 mb-2 block">
                     滑点容忍度 (%)
                   </label>
                   <input 
                     data-field="slippageTolerance"
                     type="number"
                     step="0.01"
                     min="0.01"
                     max="1"
                     className="w-full p-3 bg-slate-700 border border-slate-600 rounded-lg text-white"
                     defaultValue="0.02"
                   />
                   <p className="text-xs text-slate-400 mt-1">系统默认0.02%，超高频交易控制</p>
                 </div>

                <div>
                  <label className="text-sm font-medium text-slate-300 mb-2 block">
                    重新平衡频率
                  </label>
                  <select className="w-full p-3 bg-slate-700 border border-slate-600 rounded-lg text-white">
                    <option value="1h" selected>每小时</option>
                    <option value="4h">每4小时</option>
                    <option value="12h">每12小时</option>
                    <option value="24h">每日</option>
                    <option value="manual">手动调整</option>
                  </select>
                </div>
              </div>

              <div className="mt-6 p-4 bg-blue-500/10 border border-blue-500/30 rounded-lg">
                <h4 className="text-blue-300 font-semibold mb-2">⚙️ 高级配置说明</h4>
                <div className="text-sm text-blue-200 space-y-1">
                  <p>• <strong>币种选择</strong>: 策略支持所有币安现货交易对，建议选择流动性好的主流币种</p>
                  <p>• <strong>杠杆设置</strong>: 建议根据风险承受能力选择，新手不超过2.0x</p>
                  <p>• <strong>交易时段</strong>: 24小时交易可获得最佳收益，时段限制适合特定需求</p>
                  <p>• <strong>利润保护</strong>: 设置合理的回撤保护线，避免利润大幅回吐</p>
                  <p>• <strong>重新平衡</strong>: 频率越高资金利用率越好，但交易成本也会增加</p>
                </div>
              </div>

              <div className="mt-6 pt-6 border-t border-slate-600/30">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Button variant="outline" onClick={handleResetConfig}>重置为推荐配置</Button>
                    <Button className="bg-blue-600 hover:bg-blue-700" onClick={handleSaveConfig}>保存配置</Button>
                  </div>
                  
                  {/* 交易启动按钮 */}
                  <div className="flex items-center gap-3">
                    {!liveMetrics.isConnected ? (
                      <Button 
                        size="lg"
                        className={`bg-yellow-600 hover:bg-yellow-700 text-white min-w-[140px] transition-all duration-150 ${
                          buttonClicked ? 'scale-95 bg-yellow-500' : ''
                        }`}
                        onClick={checkInitialConnection}
                        disabled={isLoading}
                      >
                        {isLoading ? (
                          <>
                            <div className="w-5 h-5 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                            连接中...
                          </>
                        ) : (
                          <>
                            <Target className="w-5 h-5 mr-2" />
                            连接API
                          </>
                        )}
                      </Button>
                    ) : systemRunning ? (
                      <Button 
                        size="lg"
                        className={`bg-red-600 hover:bg-red-700 text-white min-w-[140px] transition-all duration-150 ${
                          buttonClicked ? 'scale-95 bg-red-500' : ''
                        }`}
                        onClick={handleSystemToggle}
                        disabled={isLoading}
                      >
                        {isLoading ? (
                          <>
                            <div className="w-5 h-5 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                            停止中...
                          </>
                        ) : (
                          <>
                            <Pause className="w-5 h-5 mr-2" />
                            停止交易
                          </>
                        )}
                      </Button>
                    ) : (
                      <Button 
                        size="lg"
                        className={`bg-green-600 hover:bg-green-700 text-white min-w-[140px] transition-all duration-150 ${
                          buttonClicked ? 'scale-95 bg-green-500' : ''
                        }`}
                        onClick={handleSystemToggle}
                        disabled={isLoading}
                      >
                        {isLoading ? (
                          <>
                            <div className="w-5 h-5 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                            启动中...
                          </>
                        ) : (
                          <>
                            <Play className="w-5 h-5 mr-2" />
                            开始交易
                          </>
                        )}
                      </Button>
                    )}
                  </div>
                </div>
                
                {/* 状态消息显示 */}
                {(statusMessage || lastOperationTime) && (
                  <div className="mt-4 p-3 bg-slate-700/50 border border-slate-600/50 rounded-lg">
                    {statusMessage && (
                      <div className={`text-sm font-medium mb-1 ${
                        statusMessage.includes('❌') ? 'text-red-400' :
                        statusMessage.includes('✅') ? 'text-green-400' :
                        statusMessage.includes('🔄') || statusMessage.includes('🚀') ? 'text-blue-400' :
                        'text-white'
                      }`}>
                        {statusMessage}
                      </div>
                    )}
                    {lastOperationTime && (
                      <div className="text-xs text-slate-400">
                        {lastOperationTime}
                      </div>
                    )}
                  </div>
                )}

                {/* 交易状态提示 */}
                {liveMetrics.isConnected && !statusMessage && (
                  <div className="mt-4 p-3 bg-blue-500/10 border border-blue-500/30 rounded-lg">
                    <div className="flex items-center gap-2 text-blue-300 text-sm">
                      <div className={`w-2 h-2 rounded-full ${systemRunning ? 'bg-green-400 animate-pulse' : 'bg-yellow-400'}`}></div>
                      <span>
                        {systemRunning ? 
                          `正在交易 ${selectedSymbol}，实时监控页面将显示WebSocket数据` : 
                          `已连接API，点击"开始交易"启动策略并开始实时监控`
                        }
                      </span>
                    </div>
                    {/* 调试信息 */}
                    <div className="mt-2 text-xs text-slate-400 space-y-1">
                      <div>UI状态: systemRunning = {systemRunning.toString()}</div>
                      <div>LocalStorage: trading_status = {localStorage.getItem('trading_status') || '未设置'}</div>
                      <div>API状态: isTrading = {liveMetrics.isTrading?.toString() || '未知'}</div>
                      <div>API连接: isConnected = {liveMetrics.isConnected.toString()}</div>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 策略监控 */}
        <TabsContent value="monitor" className="space-y-6">
          {/* 四策略实时监控 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {strategyStats.map((strategy, index) => {
              // 根据策略类型设置图标和颜色
              const getStrategyConfig = (name: string) => {
                switch (name) {
                  case 'scalping':
                    return { icon: Zap, color: 'blue', bgColor: 'bg-blue-500/10', borderColor: 'border-blue-500/30', textColor: 'text-blue-400' }
                  case 'trend':
                    return { icon: TrendingDown, color: 'purple', bgColor: 'bg-purple-500/10', borderColor: 'border-purple-500/30', textColor: 'text-purple-400' }
                  case 'grid':
                    return { icon: BarChart3, color: 'orange', bgColor: 'bg-orange-500/10', borderColor: 'border-orange-500/30', textColor: 'text-orange-400' }
                  case 'super_trend':
                    return { icon: Target, color: 'red', bgColor: 'bg-red-500/10', borderColor: 'border-red-500/30', textColor: 'text-red-400' }
                  default:
                    return { icon: BarChart3, color: 'gray', bgColor: 'bg-gray-500/10', borderColor: 'border-gray-500/30', textColor: 'text-gray-400' }
                }
              }
              
              const config = getStrategyConfig(strategy.name)
              const Icon = config.icon
              
              return (
                <Card key={strategy.name} className="bg-slate-800/50 border-slate-700/50">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Icon className={`w-5 h-5 ${config.textColor}`} />
                        {strategy.displayName}
                      </div>
                      <div className={`w-3 h-3 rounded-full ${
                        strategy.isActive ? 'bg-green-500 animate-pulse' : 
                        systemRunning ? 'bg-yellow-500' : 'bg-gray-500'
                      }`}></div>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className={`text-center p-3 ${config.bgColor} border ${config.borderColor} rounded-lg`}>
                        <p className={`${config.textColor.replace('400', '300')} text-sm`}>贡献占比</p>
                        <p className={`text-xl font-bold ${config.textColor}`}>{strategy.contributionRatio.toFixed(1)}%</p>
                      </div>
                      <div className="text-center p-3 bg-green-500/10 border border-green-500/30 rounded-lg">
                        <p className="text-green-300 text-sm">今日盈亏</p>
                        <p className={`text-xl font-bold ${strategy.todayPnl >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                          {strategy.todayPnl >= 0 ? '+' : ''}${strategy.todayPnl.toFixed(0)}
                        </p>
                      </div>
                    </div>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-slate-400">历史交易:</span>
                        <span className="text-white">{strategy.historicalTrades.toLocaleString()}次</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-400">今日交易:</span>
                        <span className="text-white">{strategy.todayTrades.toLocaleString()}次</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-400">平均持仓:</span>
                        <span className="text-white">{strategy.avgHoldTime}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-400">成功率:</span>
                        <span className="text-green-400">{strategy.winRate.toFixed(1)}%</span>
                      </div>
                      {/* 显示策略特定参数 */}
                      {Object.entries(strategy.specificParams).map(([key, value]) => (
                        <div key={key} className="flex justify-between">
                          <span className="text-slate-400">{key}:</span>
                          <span className="text-white">{value}</span>
                        </div>
                      ))}
                      <div className="flex justify-between">
                        <span className="text-slate-400">状态:</span>
                        <span className={strategy.isActive ? 'text-green-400' : 'text-gray-400'}>
                          {strategy.currentStatus}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
            
            {/* 如果没有策略数据，显示加载中或默认数据 */}
            {strategyStats.length === 0 && (
              <Card className="bg-slate-800/50 border-slate-700/50">
                <CardHeader>
                  <CardTitle className="text-white flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Zap className="w-5 h-5 text-blue-400" />
                      策略数据加载中...
                    </div>
                    <div className="w-3 h-3 rounded-full bg-gray-500"></div>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="text-center text-slate-400">
                    等待策略数据更新...
                  </div>
                </CardContent>
              </Card>
            )}


          </div>

          {/* 系统综合状态 */}
          <Card className="bg-slate-800/50 border-slate-700/50">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Shield className="w-5 h-5" />
                系统综合监控
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                {/* 动态杠杆监控 */}
                <div className="text-center p-4 bg-blue-500/10 border border-blue-500/30 rounded-lg">
                  <div className="flex items-center justify-center gap-2 mb-2">
                    <Zap className="w-4 h-4 text-blue-400" />
                    <span className="text-blue-300 text-sm">当前杠杆</span>
                  </div>
                  <p className="text-2xl font-bold text-blue-400">
                    {systemStats ? `${systemStats.dynamicLeverage.toFixed(2)}x` : '1.35x'}
                  </p>
                  <p className="text-xs text-blue-300 mt-1">实时调整</p>
                </div>

                {/* 市场状态识别 */}
                <div className="text-center p-4 bg-green-500/10 border border-green-500/30 rounded-lg">
                  <div className="flex items-center justify-center gap-2 mb-2">
                    <BarChart3 className="w-4 h-4 text-green-400" />
                    <span className="text-green-300 text-sm">市场状态</span>
                  </div>
                  <p className="text-2xl font-bold text-green-400">
                    {systemStats ? systemStats.marketState : '震荡'}
                  </p>
                  <p className="text-xs text-green-300 mt-1">自动识别</p>
                </div>

                {/* 风险监控 */}
                <div className="text-center p-4 bg-orange-500/10 border border-orange-500/30 rounded-lg">
                  <div className="flex items-center justify-center gap-2 mb-2">
                    <AlertTriangle className="w-4 h-4 text-orange-400" />
                    <span className="text-orange-300 text-sm">风险等级</span>
                  </div>
                  <p className="text-2xl font-bold text-orange-400">
                    {systemStats ? systemStats.riskLevel : (liveMetrics.riskLevel === 'LOW' ? '低' : liveMetrics.riskLevel === 'MEDIUM' ? '中' : '高')}
                  </p>
                  <p className="text-xs text-orange-300 mt-1">实时评估</p>
                </div>

                {/* 执行延迟 */}
                <div className="text-center p-4 bg-purple-500/10 border border-purple-500/30 rounded-lg">
                  <div className="flex items-center justify-center gap-2 mb-2">
                    <Clock className="w-4 h-4 text-purple-400" />
                    <span className="text-purple-300 text-sm">执行延迟</span>
                  </div>
                  <p className="text-2xl font-bold text-purple-400">
                    {systemStats ? `${systemStats.executionDelay}ms` : '12ms'}
                  </p>
                  <p className="text-xs text-purple-300 mt-1">毫秒级</p>
                </div>
              </div>

              {/* 策略协调状态 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <h4 className="text-white font-semibold">策略协调状态</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between items-center">
                      <span className="text-slate-400">主策略:</span>
                      <span className="text-blue-400">
                        {systemStats ? systemStats.coordination.primaryStrategy : '超短线 (活跃)'}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-slate-400">辅助策略:</span>
                      <span className="text-purple-400">
                        {systemStats ? systemStats.coordination.auxiliaryStrategy : '趋势跟踪 (活跃)'}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-slate-400">基础策略:</span>
                      <span className="text-orange-400">
                        {systemStats ? systemStats.coordination.baseStrategy : '网格 (正常)'}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-slate-400">备用策略:</span>
                      <span className="text-gray-400">
                        {systemStats ? systemStats.coordination.backupStrategy : '超强趋势 (待机)'}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-slate-400">策略切换:</span>
                      <span className="text-green-400">
                        {systemStats ? systemStats.coordination.switchMode : '自动 (智能)'}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <h4 className="text-white font-semibold">系统健康度</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between items-center">
                      <span className="text-slate-400">API连接:</span>
                      <span className={
                        systemStats ? 
                          (systemStats.health.apiConnection === 'normal' ? 'text-green-400' : 'text-red-400') :
                          (liveMetrics.isConnected ? 'text-green-400' : 'text-red-400')
                      }>
                        {systemStats ? 
                          (systemStats.health.apiConnection === 'normal' ? '正常' : '断开') :
                          (liveMetrics.isConnected ? '正常' : '断开')
                        }
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-slate-400">数据同步:</span>
                      <span className="text-green-400">
                        {systemStats ? 
                          (systemStats.health.dataSync === 'realtime' ? '实时' : '延迟') : 
                          '实时'
                        }
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-slate-400">订单执行:</span>
                      <span className="text-green-400">
                        {systemStats ? 
                          (systemStats.health.orderExecution === 'normal' ? '正常' : '异常') : 
                          '正常'
                        }
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-slate-400">风控状态:</span>
                      <span className="text-green-400">
                        {systemStats ? 
                          (systemStats.health.riskControl === 'enabled' ? '已启用' : '已禁用') : 
                          '已启用'
                        }
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-slate-400">运行时间:</span>
                      <span className="text-white">
                        {systemStats ? systemStats.health.runTime : (systemRunning ? '运行中' : '0分钟')}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* 监控提示 */}
              <div className="mt-6 p-4 bg-blue-500/10 border border-blue-500/30 rounded-lg">
                <h4 className="text-blue-300 font-semibold mb-2">📊 实时监控说明</h4>
                <div className="text-sm text-blue-200 space-y-1">
                  <p>• <strong>策略权重动态调整</strong>: 根据市场状态自动调整各策略的参与度</p>
                  <p>• <strong>智能风险控制</strong>: 实时监控回撤和杠杆，超限自动降低风险</p>
                  <p>• <strong>毫秒级执行</strong>: 66,312次历史交易验证的超高频执行能力</p>
                  <p>• <strong>24/7监控</strong>: 全天候监控市场变化和策略表现</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>



        {/* 风控设置 */}
        <TabsContent value="risk" className="space-y-6">
          {/* 风险评估 */}
          <Card className="bg-slate-800/50 border-slate-700/50">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <AlertTriangle className="w-5 h-5" />
                策略终极版风险评估
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                {/* 风险等级 */}
                <div className="text-center p-4 bg-orange-500/10 border border-orange-500/30 rounded-lg">
                  <div className="flex items-center justify-center gap-2 mb-2">
                    <AlertTriangle className="w-5 h-5 text-orange-400" />
                    <span className="text-orange-300 text-sm">风险等级</span>
                  </div>
                  <p className="text-2xl font-bold text-orange-400">中高风险</p>
                  <p className="text-xs text-orange-300 mt-1">回撤可控</p>
                </div>

                {/* 风险承受度 */}
                <div className="text-center p-4 bg-orange-500/10 border border-orange-500/30 rounded-lg">
                  <div className="flex items-center justify-center gap-2 mb-2">
                    <Shield className="w-5 h-5 text-orange-400" />
                    <span className="text-orange-300 text-sm">适合人群</span>
                  </div>
                  <p className="text-2xl font-bold text-orange-400">激进型</p>
                  <p className="text-xs text-orange-300 mt-1">高风险承受</p>
                </div>

                {/* 建议资金比例 */}
                <div className="text-center p-4 bg-green-500/10 border border-green-500/30 rounded-lg">
                  <div className="flex items-center justify-center gap-2 mb-2">
                    <TrendingDown className="w-5 h-5 text-green-400" />
                    <span className="text-green-300 text-sm">建议配置</span>
                  </div>
                  <p className="text-2xl font-bold text-green-400">≤20%</p>
                  <p className="text-xs text-green-300 mt-1">总资产占比</p>
                </div>
              </div>

              {/* 风险指标分析 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="text-white font-semibold">风险指标详情</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-slate-400">最大回撤:</span>
                      <div className="flex items-center gap-2">
                        <span className="text-orange-400 font-semibold">-33.24%</span>
                        <span className="text-xs text-green-300 bg-green-500/20 px-2 py-1 rounded">风险可控</span>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-slate-400">回撤限制:</span>
                      <span className="text-green-400">≤35%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-slate-400">回撤持续时间:</span>
                      <span className="text-orange-400">长期(数月至数年)</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-slate-400">波动率(年化):</span>
                      <span className="text-red-400">极高 (&gt;100%)</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-slate-400">VaR (95%):</span>
                      <span className="text-red-400">-35%/月</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-slate-400">最大连续亏损:</span>
                      <span className="text-red-400">-84% (2022熊市)</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="text-white font-semibold">风险缓解建议</h4>
                  <div className="space-y-3 text-sm">
                    <div className="p-3 bg-blue-500/10 border border-blue-500/30 rounded-lg">
                      <h5 className="text-blue-300 font-medium mb-1">💡 仓位配置</h5>
                      <p className="text-blue-200">建议配置10-20%资金，33.24%回撤风险可控</p>
                    </div>
                    <div className="p-3 bg-green-500/10 border border-green-500/30 rounded-lg">
                      <h5 className="text-green-300 font-medium mb-1">⚡ 技术保障</h5>
                      <p className="text-green-200">确保稳定网络连接和API服务，避免超高频交易中断</p>
                    </div>
                    <div className="p-3 bg-purple-500/10 border border-purple-500/30 rounded-lg">
                      <h5 className="text-purple-300 font-medium mb-1">🔄 动态监控</h5>
                      <p className="text-purple-200">实时监控杠杆使用率和回撤情况，及时调整参数</p>
                    </div>
                    <div className="p-3 bg-orange-500/10 border border-orange-500/30 rounded-lg">
                      <h5 className="text-orange-300 font-medium mb-1">⚠️ 成本控制</h5>
                      <p className="text-orange-200">关注高频交易的手续费累积，优化交易成本</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* 风险警告 */}
              <div className="mt-6 p-4 bg-orange-500/10 border border-orange-500/30 rounded-lg">
                <h4 className="text-orange-300 font-semibold mb-2">⚠️ 重要风险提醒</h4>
                <div className="text-sm text-orange-200 space-y-1">
                  <p>• <strong>高频风险</strong>: 66,312次交易产生的手续费和滑点成本需要考虑</p>
                  <p>• <strong>技术风险</strong>: 多策略系统复杂，需要稳定的技术基础设施</p>
                  <p>• <strong>市场风险</strong>: 33.24%最大回撤虽可控，但仍需充分的心理准备</p>
                  <p>• <strong>杠杆风险</strong>: 平均1.27倍杠杆放大收益的同时也放大了风险</p>
                  <p>• <strong>连接风险</strong>: 实盘交易需要稳定的网络和API连接</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 风控参数设置 */}
          <Card className="bg-slate-800/50 border-slate-700/50">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Settings className="w-5 h-5" />
                风控参数设置
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="text-sm font-medium text-slate-300 mb-2 block">最大回撤限制 (%)</label>
                  <input 
                    data-field="maxDrawdownLimit"
                    type="number" 
                    step="1"
                    className="w-full p-3 bg-slate-700 border border-slate-600 rounded-lg text-white"
                    defaultValue="35"
                  />
                  <p className="text-xs text-slate-400 mt-1">历史最大回撤: 33.24%</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-slate-300 mb-2 block">最大仓位比例 (%)</label>
                  <input 
                    data-field="maxPositionRatio"
                    type="number"
                    max="100"
                    className="w-full p-3 bg-slate-700 border border-slate-600 rounded-lg text-white"
                    defaultValue="98"
                  />
                  <p className="text-xs text-slate-400 mt-1">策略设计最大仓位</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-slate-300 mb-2 block">利润保护线 (%)</label>
                  <input 
                    data-field="profitProtectionLine"
                    type="number"
                    step="1"
                    className="w-full p-3 bg-slate-700 border border-slate-600 rounded-lg text-white"
                    defaultValue="15"
                  />
                  <p className="text-xs text-slate-400 mt-1">超过此利润后降低杠杆</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-slate-300 mb-2 block">最大杠杆倍数</label>
                  <input 
                    data-field="maxLeverageRisk"
                    type="number"
                    step="0.1"
                    className="w-full p-3 bg-slate-700 border border-slate-600 rounded-lg text-white"
                    defaultValue="3.0"
                  />
                  <p className="text-xs text-slate-400 mt-1">动态杠杆上限</p>
                </div>
              </div>

              <div className="p-4 bg-orange-500/10 border border-orange-500/30 rounded-lg">
                <h4 className="text-orange-300 font-semibold mb-2">🛡️ 智能风控机制</h4>
                <div className="text-sm text-orange-200 space-y-1">
                  <p>• <strong>三重风控</strong>: 回撤控制 + 仓位控制 + 利润保护</p>
                  <p>• <strong>动态调整</strong>: 根据市场状态和交易表现自动调整杠杆</p>
                  <p>• <strong>实时监控</strong>: 触发风控限制时暂停交易，保护资金安全</p>
                  <p>• <strong>连续亏损保护</strong>: 连续亏损时自动降低仓位和杠杆</p>
                </div>
              </div>

              <div className="flex items-center justify-end gap-3">
                <Button variant="outline">恢复默认设置</Button>
                <Button className="bg-red-600 hover:bg-red-700">应用风控设置</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}