import React, { useState, useEffect } from 'react'
import { Card } from '../ui/card'
import { Badge } from '../ui/badge'
import { globalPriceService } from '../../services/globalPriceService'
import { binanceWebSocket } from '../../services/binanceWebSocket'
import { PriceDataTest } from './PriceDataTest'

// 🔧 WebSocket连接诊断工具
export function WebSocketDiagnostics() {
  const [diagnostics, setDiagnostics] = useState({
    globalService: {
      isConnected: false,
      currentSymbol: null,
      currentPrice: 0,
      lastUpdate: 0,
      spread: 0
    },
    webSocketService: {
      connectionStatus: false,
      activeSymbol: null,
      allConnections: {}
    },
    localStorage: {
      trading_status: '',
      selected_symbol: ''
    },
    testResults: {
      canConnectToBinance: null,
      websocketSupported: null,
      networkLatency: null
    }
  })

  const [isTestingConnection, setIsTestingConnection] = useState(false)
  const [testLogs, setTestLogs] = useState<string[]>([])

  // 更新诊断信息
  useEffect(() => {
    const updateDiagnostics = () => {
      setDiagnostics({
        globalService: {
          isConnected: globalPriceService.isConnected(),
          currentSymbol: globalPriceService.getCurrentSymbol(),
          currentPrice: globalPriceService.getCurrentPrice(),
          lastUpdate: globalPriceService.getCurrentState().lastUpdate,
          spread: globalPriceService.getSpread()
        },
        webSocketService: {
          connectionStatus: binanceWebSocket.getConnectionStatus(),
          activeSymbol: binanceWebSocket.getActiveSymbol(),
          allConnections: binanceWebSocket.getAllConnectionStatus()
        },
        localStorage: {
          trading_status: localStorage.getItem('trading_status') || '',
          selected_symbol: localStorage.getItem('selected_symbol') || ''
        },
        testResults: diagnostics.testResults
      })
    }

    updateDiagnostics()
    const interval = setInterval(updateDiagnostics, 2000)
    return () => clearInterval(interval)
  }, [])

  // 测试网络连接
  const testNetworkConnection = async () => {
    setIsTestingConnection(true)
    setTestLogs(['🔧 开始网络连接测试...'])
    
    try {
      // 测试1: WebSocket支持
      const wsSupported = typeof WebSocket !== 'undefined'
      setTestLogs(prev => [...prev, `✅ WebSocket支持: ${wsSupported ? '是' : '否'}`])
      
      // 测试2: 网络延迟
      const startTime = Date.now()
      try {
        const response = await fetch('https://api.binance.com/api/v3/ping')
        const latency = Date.now() - startTime
        setTestLogs(prev => [...prev, `✅ 币安API延迟: ${latency}ms`])
        
        if (response.ok) {
          setTestLogs(prev => [...prev, '✅ 币安API可访问'])
        } else {
          setTestLogs(prev => [...prev, '❌ 币安API响应异常'])
        }
      } catch (error) {
        setTestLogs(prev => [...prev, '❌ 币安API连接失败: ' + (error as Error).message])
      }
      
      // 测试3: WebSocket连接
      try {
        setTestLogs(prev => [...prev, '🔧 测试WebSocket连接...'])
        const testWs = new WebSocket('wss://fstream.binance.com/ws/btcusdt@ticker')
        
        const testPromise = new Promise((resolve, reject) => {
          const timeout = setTimeout(() => {
            testWs.close()
            reject(new Error('连接超时'))
          }, 10000)
          
          testWs.onopen = () => {
            clearTimeout(timeout)
            setTestLogs(prev => [...prev, '✅ WebSocket连接成功'])
            testWs.close()
            resolve(true)
          }
          
          testWs.onerror = (error) => {
            clearTimeout(timeout)
            reject(error)
          }
          
          testWs.onmessage = (event) => {
            try {
              const data = JSON.parse(event.data)
              if (data.e === '24hrTicker') {
                setTestLogs(prev => [...prev, `✅ 收到测试数据: ${data.s} $${parseFloat(data.c).toFixed(2)}`])
              }
            } catch (e) {
              // 忽略解析错误
            }
          }
        })
        
        await testPromise
        setTestLogs(prev => [...prev, '✅ WebSocket功能测试通过'])
        
      } catch (error) {
        setTestLogs(prev => [...prev, '❌ WebSocket连接测试失败: ' + (error as Error).message])
      }
      
      setTestLogs(prev => [...prev, '🎯 网络连接测试完成'])
      
    } catch (error) {
      setTestLogs(prev => [...prev, '❌ 测试过程出错: ' + (error as Error).message])
    } finally {
      setIsTestingConnection(false)
    }
  }

  // 强制重连
  const forceReconnect = () => {
    setTestLogs(['🔄 执行强制重连...'])
    
    // 1. 设置默认状态
    localStorage.setItem('trading_status', 'active')
    localStorage.setItem('selected_symbol', 'BTCUSDT')
    setTestLogs(prev => [...prev, '✅ localStorage状态已设置'])
    
    // 2. 重连全局价格服务
    globalPriceService.reconnect()
    setTestLogs(prev => [...prev, '✅ 全局价格服务重连已触发'])
    
    // 3. 等待连接结果
    setTimeout(() => {
      const isConnected = globalPriceService.isConnected()
      const currentPrice = globalPriceService.getCurrentPrice()
      
      if (isConnected && currentPrice > 0) {
        setTestLogs(prev => [...prev, `✅ 重连成功! 当前价格: $${currentPrice.toFixed(2)}`])
      } else {
        setTestLogs(prev => [...prev, '⚠️ 重连可能未完全成功，请检查网络连接'])
      }
    }, 5000)
  }

  // 清除缓存
  const clearCache = () => {
    localStorage.removeItem('trading_status')
    localStorage.removeItem('selected_symbol')
    setTestLogs(['🧹 localStorage缓存已清除'])
    
    setTimeout(() => {
      window.location.reload()
    }, 1000)
  }

  return (
    <div className="space-y-6">
      {/* 价格数据流测试 */}
      <PriceDataTest />

      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">🔧 WebSocket连接诊断</h2>
        <div className="flex space-x-2">
          <button
            onClick={testNetworkConnection}
            disabled={isTestingConnection}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-400"
          >
            {isTestingConnection ? '测试中...' : '🧪 网络测试'}
          </button>
          <button
            onClick={forceReconnect}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            🔄 强制重连
          </button>
          <button
            onClick={clearCache}
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
          >
            🧹 清除缓存
          </button>
        </div>
      </div>

      {/* 当前状态 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="p-4">
          <h3 className="font-semibold mb-3">🌐 全局价格服务</h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span>连接状态:</span>
              <Badge className={diagnostics.globalService.isConnected ? 'bg-green-500' : 'bg-red-500'}>
                {diagnostics.globalService.isConnected ? '已连接' : '断开'}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span>监控币种:</span>
              <span>{diagnostics.globalService.currentSymbol || '无'}</span>
            </div>
            <div className="flex justify-between">
              <span>当前价格:</span>
              <span>${diagnostics.globalService.currentPrice.toFixed(2)}</span>
            </div>
            <div className="flex justify-between">
              <span>买卖价差:</span>
              <span>${diagnostics.globalService.spread.toFixed(4)}</span>
            </div>
            <div className="flex justify-between">
              <span>最后更新:</span>
              <span>{diagnostics.globalService.lastUpdate ? 
                new Date(diagnostics.globalService.lastUpdate).toLocaleTimeString() : '无'}</span>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <h3 className="font-semibold mb-3">🔗 WebSocket服务</h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span>服务状态:</span>
              <Badge className={diagnostics.webSocketService.connectionStatus ? 'bg-green-500' : 'bg-red-500'}>
                {diagnostics.webSocketService.connectionStatus ? '运行中' : '停止'}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span>活跃币种:</span>
              <span>{diagnostics.webSocketService.activeSymbol || '无'}</span>
            </div>
            <div className="mt-3">
              <span className="font-medium">连接详情:</span>
              <div className="mt-1 space-y-1">
                {Object.entries(diagnostics.webSocketService.allConnections).map(([stream, connected]) => (
                  <div key={stream} className="flex justify-between text-xs">
                    <span>{stream}:</span>
                    <Badge className={connected ? 'bg-green-500' : 'bg-red-500'} size="sm">
                      {connected ? '✓' : '✗'}
                    </Badge>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <h3 className="font-semibold mb-3">💾 本地存储</h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span>交易状态:</span>
              <Badge className={diagnostics.localStorage.trading_status === 'active' ? 'bg-green-500' : 'bg-yellow-500'}>
                {diagnostics.localStorage.trading_status || '未设置'}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span>选择币种:</span>
              <span>{diagnostics.localStorage.selected_symbol || '未设置'}</span>
            </div>
          </div>
        </Card>
      </div>

      {/* 测试日志 */}
      {testLogs.length > 0 && (
        <Card className="p-4">
          <h3 className="font-semibold mb-3">📋 测试日志</h3>
          <div className="bg-gray-900 text-green-400 p-3 rounded font-mono text-sm max-h-60 overflow-y-auto">
            {testLogs.map((log, index) => (
              <div key={index} className="mb-1">
                [{new Date().toLocaleTimeString()}] {log}
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* 问题排查指南 */}
      <Card className="p-4">
        <h3 className="font-semibold mb-3">🔍 问题排查指南</h3>
        <div className="space-y-3 text-sm">
          <div>
            <strong>价格显示为固定值或$0.00:</strong>
            <ul className="list-disc list-inside ml-4 mt-1 space-y-1">
              <li>检查网络连接是否正常</li>
              <li>确认币安API是否可访问</li>
              <li>点击"强制重连"重新建立连接</li>
              <li>检查浏览器控制台是否有错误信息</li>
            </ul>
          </div>
          <div>
            <strong>WebSocket连接失败:</strong>
            <ul className="list-disc list-inside ml-4 mt-1 space-y-1">
              <li>检查防火墙设置</li>
              <li>确认代理设置（如果使用）</li>
              <li>尝试刷新页面</li>
              <li>清除浏览器缓存</li>
            </ul>
          </div>
          <div>
            <strong>数据更新缓慢:</strong>
            <ul className="list-disc list-inside ml-4 mt-1 space-y-1">
              <li>检查网络延迟</li>
              <li>关闭其他占用带宽的应用</li>
              <li>尝试更换网络环境</li>
            </ul>
          </div>
        </div>
      </Card>
    </div>
  )
}
