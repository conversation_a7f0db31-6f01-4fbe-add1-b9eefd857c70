import React, { useState, useEffect } from 'react'
import { Card } from '../ui/card'
import { Badge } from '../ui/badge'
import { LIVE_TRADING_CONFIG, getCurrentPhaseConfig, setVerificationPhase, shouldStopTrading } from '../../config/liveTrading.config'

// 🚀 实盘验证启动器组件
export function LiveTradingLauncher() {
  const [currentPhase, setCurrentPhase] = useState<'phase1' | 'phase2' | 'phase3'>('phase1')
  const [isRunning, setIsRunning] = useState(false)
  const [startTime, setStartTime] = useState<number | null>(null)
  const [metrics, setMetrics] = useState({
    currentEquity: 1000,
    dailyReturn: 0,
    totalReturn: 0,
    drawdown: 0,
    winRate: 0,
    orderSuccessRate: 100,
    consecutiveLosses: 0,
    dailyLoss: 0,
    apiLatency: 150
  })

  const [systemStatus, setSystemStatus] = useState({
    apiConnected: false,
    wsConnected: false,
    memoryUsage: 0.45,
    cpuUsage: 0.30,
    lastCheck: Date.now()
  })

  const [checklistStatus, setChecklistStatus] = useState({
    verifyApiCredentials: false,
    checkAccountBalance: false,
    validateNetworkConnection: false,
    initializeMonitoring: false,
    setupEmergencyStops: false,
    verifySystemResources: false
  })

  const phaseConfig = getCurrentPhaseConfig()

  // 模拟数据更新
  useEffect(() => {
    if (!isRunning) return

    const interval = setInterval(() => {
      setMetrics(prev => ({
        ...prev,
        currentEquity: prev.currentEquity + (Math.random() - 0.5) * 10,
        dailyReturn: (Math.random() - 0.4) * 0.02, // -0.8% to +1.2%
        totalReturn: prev.totalReturn + (Math.random() - 0.45) * 0.005,
        drawdown: Math.max(0, prev.drawdown + (Math.random() - 0.7) * 0.01),
        winRate: 0.6 + Math.random() * 0.3,
        orderSuccessRate: 95 + Math.random() * 5,
        apiLatency: 100 + Math.random() * 200
      }))

      setSystemStatus(prev => ({
        ...prev,
        apiConnected: Math.random() > 0.05,
        wsConnected: Math.random() > 0.02,
        memoryUsage: 0.4 + Math.random() * 0.3,
        cpuUsage: 0.2 + Math.random() * 0.4,
        lastCheck: Date.now()
      }))
    }, 5000)

    return () => clearInterval(interval)
  }, [isRunning])

  // 检查是否应该停止交易
  useEffect(() => {
    if (!isRunning) return

    const stopCheck = shouldStopTrading(metrics)
    if (stopCheck.shouldStop) {
      console.warn(`⚠️ 自动停止交易: ${stopCheck.reason}`)
      setIsRunning(false)
      alert(`🚨 交易已自动停止\n原因: ${stopCheck.reason}\n建议行动: ${stopCheck.action}`)
    }
  }, [metrics, isRunning])

  const handleStartTrading = async () => {
    // 检查启动条件
    const allChecked = Object.values(checklistStatus).every(status => status)
    if (!allChecked) {
      alert('请完成所有启动检查项目')
      return
    }

    setIsRunning(true)
    setStartTime(Date.now())
    setVerificationPhase(currentPhase)
    
    console.log(`🚀 启动${phaseConfig.name}验证`)
    console.log(`📊 配置参数:`, phaseConfig)
  }

  const handleStopTrading = () => {
    setIsRunning(false)
    setStartTime(null)
    console.log('⏹️ 停止实盘验证')
  }

  const handleEmergencyStop = () => {
    setIsRunning(false)
    setStartTime(null)
    alert('🚨 紧急停止已执行！\n请检查账户状态并评估风险。')
    console.error('🚨 紧急停止触发')
  }

  const toggleChecklistItem = (item: keyof typeof checklistStatus) => {
    setChecklistStatus(prev => ({
      ...prev,
      [item]: !prev[item]
    }))
  }

  const getStatusColor = (value: number, thresholds: [number, number]) => {
    if (value < thresholds[0]) return 'text-green-500'
    if (value < thresholds[1]) return 'text-yellow-500'
    return 'text-red-500'
  }

  const formatDuration = (ms: number) => {
    const hours = Math.floor(ms / (1000 * 60 * 60))
    const minutes = Math.floor((ms % (1000 * 60 * 60)) / (1000 * 60))
    return `${hours}小时${minutes}分钟`
  }

  return (
    <div className="space-y-6">
      {/* 标题和状态 */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">🚀 实盘验证启动器</h2>
        <div className="flex items-center space-x-4">
          <Badge className={isRunning ? 'bg-green-500' : 'bg-gray-500'}>
            {isRunning ? '运行中' : '已停止'}
          </Badge>
          {startTime && (
            <span className="text-sm text-muted-foreground">
              运行时间: {formatDuration(Date.now() - startTime)}
            </span>
          )}
        </div>
      </div>

      {/* 阶段选择 */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">📊 验证阶段选择</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {Object.entries(LIVE_TRADING_CONFIG.phases).map(([key, phase]) => (
            <div
              key={key}
              className={`p-4 border rounded-lg cursor-pointer transition-all ${
                currentPhase === key ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
              }`}
              onClick={() => setCurrentPhase(key as any)}
            >
              <h4 className="font-medium">{phase.name}</h4>
              <div className="text-sm text-muted-foreground mt-2">
                <p>资金: {phase.maxCapital} USDT</p>
                <p>仓位: {(phase.maxPositionRatio * 100).toFixed(0)}%</p>
                <p>时长: {phase.duration / (24 * 60 * 60 * 1000)}天</p>
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* 启动检查清单 */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">✅ 启动检查清单</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {Object.entries(checklistStatus).map(([key, checked]) => (
            <div
              key={key}
              className="flex items-center space-x-3 p-3 border rounded cursor-pointer hover:bg-gray-50"
              onClick={() => toggleChecklistItem(key as any)}
            >
              <div className={`w-4 h-4 rounded border-2 flex items-center justify-center ${
                checked ? 'bg-green-500 border-green-500' : 'border-gray-300'
              }`}>
                {checked && <span className="text-white text-xs">✓</span>}
              </div>
              <span className={checked ? 'text-green-600' : 'text-gray-600'}>
                {key.replace(/([A-Z])/g, ' $1').toLowerCase()}
              </span>
            </div>
          ))}
        </div>
      </Card>

      {/* 实时监控 */}
      {isRunning && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 交易指标 */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">📈 交易指标</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-muted-foreground">当前权益</span>
                <span className="font-medium">{metrics.currentEquity.toFixed(2)} USDT</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">日收益率</span>
                <span className={`font-medium ${getStatusColor(Math.abs(metrics.dailyReturn), [0.01, 0.02])}`}>
                  {(metrics.dailyReturn * 100).toFixed(2)}%
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">总收益率</span>
                <span className={`font-medium ${metrics.totalReturn >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                  {(metrics.totalReturn * 100).toFixed(2)}%
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">最大回撤</span>
                <span className={`font-medium ${getStatusColor(metrics.drawdown, [0.03, 0.05])}`}>
                  {(metrics.drawdown * 100).toFixed(2)}%
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">胜率</span>
                <span className={`font-medium ${getStatusColor(1 - metrics.winRate, [0.3, 0.5])}`}>
                  {(metrics.winRate * 100).toFixed(1)}%
                </span>
              </div>
            </div>
          </Card>

          {/* 系统状态 */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">🖥️ 系统状态</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-muted-foreground">API连接</span>
                <Badge className={systemStatus.apiConnected ? 'bg-green-500' : 'bg-red-500'}>
                  {systemStatus.apiConnected ? '已连接' : '断开'}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">WebSocket</span>
                <Badge className={systemStatus.wsConnected ? 'bg-green-500' : 'bg-red-500'}>
                  {systemStatus.wsConnected ? '已连接' : '断开'}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">内存使用</span>
                <span className={`font-medium ${getStatusColor(systemStatus.memoryUsage, [0.7, 0.85])}`}>
                  {(systemStatus.memoryUsage * 100).toFixed(1)}%
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">CPU使用</span>
                <span className={`font-medium ${getStatusColor(systemStatus.cpuUsage, [0.6, 0.8])}`}>
                  {(systemStatus.cpuUsage * 100).toFixed(1)}%
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">API延迟</span>
                <span className={`font-medium ${getStatusColor(metrics.apiLatency, [500, 1000])}`}>
                  {metrics.apiLatency.toFixed(0)}ms
                </span>
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* 控制按钮 */}
      <div className="flex justify-center space-x-4">
        {!isRunning ? (
          <button
            onClick={handleStartTrading}
            disabled={!Object.values(checklistStatus).every(status => status)}
            className="px-6 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            🚀 启动实盘验证
          </button>
        ) : (
          <>
            <button
              onClick={handleStopTrading}
              className="px-6 py-3 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600"
            >
              ⏹️ 正常停止
            </button>
            <button
              onClick={handleEmergencyStop}
              className="px-6 py-3 bg-red-500 text-white rounded-lg hover:bg-red-600"
            >
              🚨 紧急停止
            </button>
          </>
        )}
      </div>

      {/* 当前阶段信息 */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">📋 当前阶段: {phaseConfig.name}</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <span className="text-muted-foreground">最大资金</span>
            <p className="font-medium">{phaseConfig.maxCapital} USDT</p>
          </div>
          <div>
            <span className="text-muted-foreground">最大仓位</span>
            <p className="font-medium">{(phaseConfig.maxPositionRatio * 100).toFixed(0)}%</p>
          </div>
          <div>
            <span className="text-muted-foreground">单笔限额</span>
            <p className="font-medium">{phaseConfig.maxSingleTrade} USDT</p>
          </div>
          <div>
            <span className="text-muted-foreground">目标收益</span>
            <p className="font-medium">{(phaseConfig.targetDailyReturn * 100).toFixed(1)}%/日</p>
          </div>
        </div>
      </Card>
    </div>
  )
}
