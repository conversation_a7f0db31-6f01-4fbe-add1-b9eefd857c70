// 🌐 代理检测测试脚本
console.log('🔍 启动代理配置检测测试...')

// 检测当前代理配置状态
async function detectProxyStatus() {
  console.log('\n🌐 ===== 代理配置检测 =====')
  
  try {
    // 1. 检查localStorage中的代理配置
    console.log('1️⃣ 检查localStorage代理配置...')
    const localStorageConfig = localStorage.getItem('binance_api_config')
    
    if (!localStorageConfig) {
      console.error('❌ 未找到API配置')
      return false
    }
    
    const config = JSON.parse(localStorageConfig)
    console.log('📋 localStorage代理配置:')
    console.table({
      '启用代理': config.useProxy ? '✅ 是' : '❌ 否',
      '代理主机': config.proxyHost || '127.0.0.1',
      '代理端口': config.proxyPort || '7890',
      '完整地址': config.useProxy ? `${config.proxyHost || '127.0.0.1'}:${config.proxyPort || '7890'}` : '直连'
    })
    
    // 2. 检查数据库中的代理配置
    console.log('\n2️⃣ 检查数据库代理配置...')
    if (typeof strategyStateService !== 'undefined') {
      try {
        const dbConfig = await strategyStateService.getApiConfig()
        if (dbConfig) {
          console.log('📋 数据库代理配置:')
          console.table({
            '启用代理': dbConfig.useProxy ? '✅ 是' : '❌ 否',
            '代理主机': dbConfig.proxyHost || '127.0.0.1',
            '代理端口': dbConfig.proxyPort || '7890',
            '创建时间': new Date(dbConfig.createdAt).toLocaleString(),
            '更新时间': new Date(dbConfig.updatedAt).toLocaleString()
          })
          
          // 比较localStorage和数据库配置
          if (config.useProxy !== dbConfig.useProxy) {
            console.warn('⚠️ localStorage和数据库的代理设置不一致!')
            console.log(`localStorage: ${config.useProxy}, 数据库: ${dbConfig.useProxy}`)
          } else {
            console.log('✅ localStorage和数据库代理设置一致')
          }
        } else {
          console.warn('⚠️ 数据库中未找到API配置')
        }
      } catch (error) {
        console.error('❌ 数据库读取失败:', error)
      }
    } else {
      console.error('❌ strategyStateService未加载')
    }
    
    // 3. 测试binanceApi的代理检测
    console.log('\n3️⃣ 测试binanceApi代理检测...')
    if (typeof binanceApi !== 'undefined') {
      const currentProxyConfig = binanceApi.getProxyConfig()
      console.log('📋 binanceApi当前代理配置:')
      console.table({
        '启用状态': currentProxyConfig.enabled ? '✅ 启用' : '❌ 禁用',
        '代理主机': currentProxyConfig.host,
        '代理端口': currentProxyConfig.port,
        '完整地址': currentProxyConfig.enabled ? `${currentProxyConfig.host}:${currentProxyConfig.port}` : '直连'
      })
    } else {
      console.error('❌ binanceApi未加载')
    }
    
    return true
    
  } catch (error) {
    console.error('❌ 代理检测失败:', error)
    return false
  }
}

// 测试网络连接（通过代理和直连）
async function testNetworkConnection() {
  console.log('\n🌐 ===== 网络连接测试 =====')
  
  const testUrls = [
    { name: '币安现货API', url: 'https://api.binance.com/api/v3/ping' },
    { name: '币安期货API', url: 'https://fapi.binance.com/fapi/v1/ping' },
    { name: '检查IP地址', url: 'https://httpbin.org/ip' }
  ]
  
  for (const test of testUrls) {
    try {
      console.log(`🔍 测试 ${test.name}...`)
      const startTime = Date.now()
      
      const response = await fetch(test.url, {
        method: 'GET',
        timeout: 10000
      })
      
      const duration = Date.now() - startTime
      
      if (response.ok) {
        console.log(`✅ ${test.name}: 连接成功 (${duration}ms)`)
        
        // 如果是IP检查，显示当前IP
        if (test.url.includes('httpbin.org/ip')) {
          try {
            const ipData = await response.json()
            console.log(`🌐 当前IP地址: ${ipData.origin}`)
          } catch (e) {
            console.log('📍 IP信息获取失败')
          }
        }
      } else {
        console.log(`⚠️ ${test.name}: 响应异常 ${response.status} (${duration}ms)`)
      }
      
    } catch (error) {
      console.log(`❌ ${test.name}: 连接失败 - ${error.message}`)
      
      // 如果连接失败，提供解决建议
      if (error.message.includes('network') || error.message.includes('timeout')) {
        console.log('💡 建议: 检查网络连接，如在国内请启用Clash代理')
      }
    }
    
    // 添加延迟避免请求过于频繁
    await new Promise(resolve => setTimeout(resolve, 1000))
  }
}

// 强制更新代理配置
async function forceUpdateProxyConfig() {
  console.log('\n🔧 ===== 强制更新代理配置 =====')
  
  try {
    const shouldUpdate = confirm('是否强制应用Clash代理配置 (127.0.0.1:7890)?')
    if (!shouldUpdate) {
      console.log('⚪ 用户取消更新')
      return false
    }
    
    // 1. 更新localStorage
    console.log('1️⃣ 更新localStorage代理配置...')
    const currentConfig = JSON.parse(localStorage.getItem('binance_api_config') || '{}')
    const updatedConfig = {
      ...currentConfig,
      useProxy: true,
      proxyHost: '127.0.0.1',
      proxyPort: '7890'
    }
    
    localStorage.setItem('binance_api_config', JSON.stringify(updatedConfig))
    console.log('✅ localStorage代理配置已更新')
    
    // 2. 更新数据库
    if (typeof strategyStateService !== 'undefined') {
      console.log('2️⃣ 更新数据库代理配置...')
      const success = await strategyStateService.updateApiConfig({
        useProxy: true,
        proxyHost: '127.0.0.1',
        proxyPort: '7890'
      })
      
      if (success) {
        console.log('✅ 数据库代理配置已更新')
      } else {
        console.error('❌ 数据库代理配置更新失败')
      }
    }
    
    // 3. 更新binanceApi
    if (typeof binanceApi !== 'undefined') {
      console.log('3️⃣ 更新binanceApi代理配置...')
      binanceApi.setProxyConfig(true, '127.0.0.1', '7890')
      console.log('✅ binanceApi代理配置已更新')
    }
    
    console.log('🎉 代理配置强制更新完成！')
    console.log('🌐 现在所有币安API调用都将通过 127.0.0.1:7890 代理')
    
    return true
    
  } catch (error) {
    console.error('❌ 强制更新代理配置失败:', error)
    return false
  }
}

// 检查Clash代理连通性
async function checkClashProxy() {
  console.log('\n🔍 ===== Clash代理连通性检查 =====')
  
  const proxyUrl = '127.0.0.1:7890'
  
  try {
    console.log(`🔗 检查Clash代理: ${proxyUrl}`)
    
    // 注意：在浏览器环境中无法直接测试代理连接
    // 只能通过API调用的成功/失败来判断
    console.log('ℹ️ 浏览器环境限制，无法直接测试代理连接')
    console.log('💡 建议: 通过以下方式检查Clash代理状态:')
    console.log('   1. 打开Clash客户端，确认代理正在运行')
    console.log('   2. 检查Clash监听端口是否为7890')
    console.log('   3. 确认Clash处于代理模式（非直连模式）')
    console.log('   4. 在浏览器中访问 http://clash.razord.top 测试连通性')
    
    // 提供检查清单
    console.log('\n📋 Clash代理检查清单:')
    console.table({
      '检查项目': ['Clash软件运行', '监听端口7890', '代理模式开启', 'HTTP代理启用'],
      '状态': ['请手动确认', '请手动确认', '请手动确认', '请手动确认'],
      '说明': [
        '确认Clash应用正在运行',
        '确认HTTP代理端口为7890',
        '确认未处于直连(Direct)模式',
        '确认HTTP代理功能已启用'
      ]
    })
    
    return true
    
  } catch (error) {
    console.error('❌ Clash代理检查失败:', error)
    return false
  }
}

// 自动修复代理配置
async function autoFixProxyConfig() {
  console.log('\n🔧 ===== 自动修复代理配置 =====')
  
  try {
    console.log('🔄 开始自动修复流程...')
    
    // 1. 检测当前状态
    const detectionResult = await detectProxyStatus()
    if (!detectionResult) {
      console.error('❌ 代理检测失败，无法自动修复')
      return false
    }
    
    // 2. 获取当前配置
    const config = JSON.parse(localStorage.getItem('binance_api_config') || '{}')
    
    // 3. 如果未启用代理，询问是否启用
    if (!config.useProxy) {
      const shouldEnable = confirm('检测到代理未启用，是否启用Clash代理 (127.0.0.1:7890)?')
      if (shouldEnable) {
        await forceUpdateProxyConfig()
      } else {
        console.log('⚪ 用户选择不启用代理')
        return false
      }
    } else {
      console.log('✅ 代理已启用，检查配置是否正确...')
      
      // 检查代理地址是否正确
      if (config.proxyHost !== '127.0.0.1' || config.proxyPort !== '7890') {
        console.log('⚠️ 代理地址不是标准Clash配置，是否修正?')
        const shouldFix = confirm(`当前: ${config.proxyHost}:${config.proxyPort}\n修正为: 127.0.0.1:7890`)
        if (shouldFix) {
          await forceUpdateProxyConfig()
        }
      } else {
        console.log('✅ 代理配置正确')
      }
    }
    
    // 4. 测试网络连接
    console.log('🌐 测试网络连接...')
    await testNetworkConnection()
    
    console.log('✅ 自动修复完成!')
    return true
    
  } catch (error) {
    console.error('❌ 自动修复失败:', error)
    return false
  }
}

// 暴露测试函数
window.detectProxyStatus = detectProxyStatus
window.testNetworkConnection = testNetworkConnection
window.forceUpdateProxyConfig = forceUpdateProxyConfig
window.checkClashProxy = checkClashProxy
window.autoFixProxyConfig = autoFixProxyConfig

console.log(`
🌐 可用的代理测试命令:
• detectProxyStatus()      - 检测当前代理配置状态
• testNetworkConnection()  - 测试网络连接
• checkClashProxy()        - 检查Clash代理连通性
• forceUpdateProxyConfig() - 强制更新为Clash代理配置
• autoFixProxyConfig()     - 自动修复代理配置

建议执行顺序:
1. detectProxyStatus()    - 查看当前配置
2. checkClashProxy()      - 确认Clash运行状态
3. autoFixProxyConfig()   - 自动修复配置
4. testNetworkConnection() - 测试连接效果
`)

// 自动运行代理检测
detectProxyStatus() 