// 🔧 手动排查策略启动问题脚本
console.log('🔍 手动排查策略启动问题...')

// 1. 网络连接测试
async function testNetworkConnection() {
  console.log('\n🌐 ===== 网络连接测试 =====')
  
  const testUrls = [
    { name: '币安现货API', url: 'https://api.binance.com/api/v3/ping' },
    { name: '币安期货API', url: 'https://fapi.binance.com/fapi/v1/ping' },
    { name: 'Google DNS', url: 'https://8.8.8.8' },
    { name: 'Cloudflare', url: 'https://1.1.1.1' }
  ]
  
  for (const test of testUrls) {
    try {
      console.log(`测试 ${test.name}...`)
      const startTime = Date.now()
      const response = await fetch(test.url, { 
        method: 'GET',
        timeout: 5000 
      })
      const duration = Date.now() - startTime
      
      if (response.ok) {
        console.log(`✅ ${test.name}: 连接成功 (${duration}ms)`)
      } else {
        console.log(`⚠️ ${test.name}: 响应异常 ${response.status}`)
      }
    } catch (error) {
      console.log(`❌ ${test.name}: 连接失败 - ${error.message}`)
      
      // 如果是币安API失败，提供代理建议
      if (test.name.includes('币安')) {
        console.log('💡 建议: 如在国内，请配置代理或VPN')
      }
    }
  }
}

// 2. API配置检查
function checkApiConfiguration() {
  console.log('\n🔑 ===== API配置检查 =====')
  
  const apiConfig = localStorage.getItem('binance_api_config')
  
  if (!apiConfig) {
    console.error('❌ 未找到API配置')
    console.log('🔧 解决方案: 请在"系统设置"→"风险管理"中配置API凭证')
    return false
  }
  
  try {
    const config = JSON.parse(apiConfig)
    
    console.log('📋 API配置状态:')
    console.table({
      '是否有API密钥': config.apiKey ? '✅ 是' : '❌ 否',
      '是否有秘钥': config.secretKey ? '✅ 是' : '❌ 否',
      '测试网模式': config.testnet ? '✅ 是' : '❌ 否',
      '使用代理': config.useProxy ? '✅ 是' : '❌ 否',
      '代理地址': config.useProxy ? `${config.proxyHost}:${config.proxyPort}` : '未配置'
    })
    
    // 检查API密钥格式
    if (config.apiKey && config.apiKey.length < 64) {
      console.warn('⚠️ API密钥长度异常，请检查是否完整')
    }
    
    if (config.secretKey && config.secretKey.length < 64) {
      console.warn('⚠️ API秘钥长度异常，请检查是否完整')
    }
    
    return true
    
  } catch (error) {
    console.error('❌ API配置解析失败:', error)
    return false
  }
}

// 3. 策略执行器状态检查
function checkStrategyExecutor() {
  console.log('\n⚡ ===== 策略执行器检查 =====')
  
  const checks = {
    '策略执行器已定义': typeof strategyExecutor !== 'undefined',
    '币安API已定义': typeof binanceApi !== 'undefined',
    '策略状态服务已定义': typeof strategyStateService !== 'undefined',
    '价格服务已定义': typeof globalPriceService !== 'undefined'
  }
  
  console.table(checks)
  
  // 检查策略执行器方法
  if (typeof strategyExecutor !== 'undefined') {
    console.log('\n📊 策略执行器方法检查:')
    const methods = {
      'startStrategy': typeof strategyExecutor.startStrategy === 'function',
      'stopStrategy': typeof strategyExecutor.stopStrategy === 'function',
      'isStrategyRunning': typeof strategyExecutor.isStrategyRunning === 'function',
      'getStrategyStatus': typeof strategyExecutor.getStrategyStatus === 'function'
    }
    console.table(methods)
  }
  
  return Object.values(checks).every(check => check)
}

// 4. 数据库状态检查
async function checkDatabaseStatus() {
  console.log('\n💾 ===== 数据库状态检查 =====')
  
  try {
    if (typeof strategyStateService === 'undefined') {
      console.error('❌ 策略状态服务未定义')
      return false
    }
    
    // 检查活跃策略
    const activeStrategy = await strategyStateService.getActiveStrategy()
    console.log('当前活跃策略:', activeStrategy ? {
      symbol: activeStrategy.symbol,
      status: activeStrategy.runtime.currentStatus,
      startedAt: activeStrategy.runtime.startedAt ? new Date(activeStrategy.runtime.startedAt).toLocaleString() : '未启动'
    } : '无')
    
    // 检查策略历史
    const history = await strategyStateService.getStrategyHistory(5)
    console.log(`策略历史记录: ${history.length} 条`)
    
    return true
    
  } catch (error) {
    console.error('❌ 数据库检查失败:', error)
    return false
  }
}

// 5. 提供修复建议
function provideFixSuggestions() {
  console.log('\n🔧 ===== 修复建议 =====')
  
  console.log('根据诊断结果，请尝试以下修复方案:')
  
  const suggestions = [
    {
      问题: '网络连接失败',
      解决方案: [
        '1. 检查网络连接是否正常',
        '2. 如在国内，配置科学上网工具',
        '3. 在系统设置中启用代理: 127.0.0.1:7890',
        '4. 尝试切换网络环境'
      ]
    },
    {
      问题: 'API配置问题',
      解决方案: [
        '1. 前往币安官网获取新的API密钥',
        '2. 确保API权限包含现货交易',
        '3. 在系统设置中重新配置API凭证',
        '4. 检查是否选择正确的测试网/主网模式'
      ]
    },
    {
      问题: '策略执行器问题',
      解决方案: [
        '1. 刷新页面重新加载脚本',
        '2. 清除浏览器缓存',
        '3. 检查浏览器控制台错误信息',
        '4. 尝试在隐私/无痕模式下打开'
      ]
    },
    {
      问题: '数据库问题',
      解决方案: [
        '1. 运行 quickFixStartup() 重置状态',
        '2. 清除浏览器数据后重新配置',
        '3. 检查浏览器是否支持IndexedDB',
        '4. 尝试其他浏览器'
      ]
    }
  ]
  
  suggestions.forEach((suggestion, index) => {
    console.log(`\n${index + 1}. ${suggestion.问题}:`)
    suggestion.解决方案.forEach(solution => console.log(`   ${solution}`))
  })
}

// 6. 一键修复尝试
async function attemptAutoFix() {
  console.log('\n🚀 ===== 一键修复尝试 =====')
  
  try {
    console.log('1️⃣ 重置localStorage状态...')
    localStorage.setItem('trading_status', 'inactive')
    
    console.log('2️⃣ 停止所有策略...')
    if (typeof strategyStateService !== 'undefined') {
      await strategyStateService.stopAllStrategies()
    }
    
    console.log('3️⃣ 检查API配置...')
    const apiConfigExists = checkApiConfiguration()
    
    if (!apiConfigExists) {
      console.log('❌ API配置缺失，请手动配置后重试')
      return false
    }
    
    console.log('4️⃣ 测试网络连接...')
    await testNetworkConnection()
    
    console.log('5️⃣ 重新连接API...')
    const apiConfig = JSON.parse(localStorage.getItem('binance_api_config'))
    
    if (typeof binanceApi !== 'undefined') {
      await binanceApi.connect(apiConfig.apiKey, apiConfig.secretKey, apiConfig.testnet || false)
      
      if (apiConfig.useProxy) {
        binanceApi.setProxyConfig(true, apiConfig.proxyHost || '127.0.0.1', apiConfig.proxyPort || '7890')
      }
      
      console.log('✅ API重新连接完成')
    }
    
    console.log('✅ 一键修复完成，请重新尝试启动策略')
    return true
    
  } catch (error) {
    console.error('❌ 一键修复失败:', error)
    return false
  }
}

// 导出诊断函数
window.testNetwork = testNetworkConnection
window.checkApiConfig = checkApiConfiguration
window.checkExecutor = checkStrategyExecutor
window.checkDatabase = checkDatabaseStatus
window.showFixSuggestions = provideFixSuggestions
window.autoFix = attemptAutoFix

// 运行完整诊断
async function runFullDiagnosis() {
  console.log('🔍 ===== 完整策略启动诊断 =====')
  
  console.log('开始诊断，请稍候...')
  
  await testNetworkConnection()
  checkApiConfiguration()
  checkStrategyExecutor()
  await checkDatabaseStatus()
  provideFixSuggestions()
  
  console.log('\n📋 诊断完成！')
  console.log('💡 如需一键修复，请运行: autoFix()')
}

window.fullDiagnosis = runFullDiagnosis

console.log(`
🔧 可用的诊断命令:
• fullDiagnosis()    - 运行完整诊断
• testNetwork()      - 测试网络连接
• checkApiConfig()   - 检查API配置
• checkExecutor()    - 检查策略执行器
• checkDatabase()    - 检查数据库状态
• autoFix()          - 一键修复尝试

建议先运行: fullDiagnosis()
`)

// 自动运行完整诊断
runFullDiagnosis() 