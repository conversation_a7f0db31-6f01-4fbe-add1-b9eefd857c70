// 币安API服务
// 🔧 声明全局变量类型（避免TypeScript错误）
declare global {
  const strategyStateService: any
}

export interface BinanceAccount {
  totalWalletBalance: number
  availableBalance: number
  unrealizedPnL: number
  positions: BinancePosition[]
}

export interface BinancePosition {
  symbol: string
  positionAmt: number
  entryPrice: number
  markPrice: number
  unRealizedProfit: number
  percentage: number
}

export interface BinanceTrade {
  symbol: string
  orderId: number
  side: 'BUY' | 'SELL'
  quantity: number
  price: number
  commission: number
  time: number
  realizedPnl: number
}

export interface LiveTradingMetrics {
  accountBalance: number
  availableBalance: number
  currentPosition: number
  unrealizedPnL: number
  realizedPnL: number
  todayTrades: number
  currentDrawdown: number
  maxDrawdownToday: number
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  isConnected: boolean
  isTrading: boolean
}

class BinanceApiService {
  private _apiKey: string = ''
  private _apiSecret: string = ''
  private isConnected: boolean = false
  private testMode: boolean = true // 测试模式
  private proxyConfig: { enabled: boolean; host: string; port: string } = {
    enabled: false,
    host: '127.0.0.1',
    port: '7890'
  }

  // 🔧 新增：自动检测并应用代理配置
  private async autoDetectProxyConfig(): Promise<{ enabled: boolean; host: string; port: string }> {
    try {
      console.log('🔍 自动检测代理配置...')
      
      // 1. 优先从数据库读取API配置（如果有strategyStateService）
      if (typeof strategyStateService !== 'undefined') {
        try {
          const dbConfig = await strategyStateService.getApiConfig()
          if (dbConfig && dbConfig.useProxy) {
            console.log('✅ 从数据库读取代理配置: 启用')
            console.log(`🌐 代理地址: ${dbConfig.proxyHost || '127.0.0.1'}:${dbConfig.proxyPort || '7890'}`)
            return {
              enabled: true,
              host: dbConfig.proxyHost || '127.0.0.1',
              port: dbConfig.proxyPort || '7890'
            }
          } else if (dbConfig) {
            console.log('⚪ 数据库配置: 未启用代理')
            return { enabled: false, host: '127.0.0.1', port: '7890' }
          }
        } catch (dbError) {
          console.warn('⚠️ 数据库读取失败，fallback到localStorage:', dbError)
        }
      }
      
      // 2. fallback到localStorage
      const savedConfig = localStorage.getItem('binance_api_config')
      if (savedConfig) {
        const config = JSON.parse(savedConfig)
        if (config.useProxy) {
          console.log('✅ 从localStorage读取代理配置: 启用')
          console.log(`🌐 代理地址: ${config.proxyHost || '127.0.0.1'}:${config.proxyPort || '7890'}`)
          return {
            enabled: true,
            host: config.proxyHost || '127.0.0.1',
            port: config.proxyPort || '7890'
          }
        } else {
          console.log('⚪ localStorage配置: 未启用代理')
          return { enabled: false, host: '127.0.0.1', port: '7890' }
        }
      }
      
      console.log('⚪ 未找到代理配置，使用直连')
      return { enabled: false, host: '127.0.0.1', port: '7890' }
      
    } catch (error) {
      console.error('❌ 代理配置检测失败:', error)
      return { enabled: false, host: '127.0.0.1', port: '7890' }
    }
  }

  // 🔧 增强：连接时自动检测代理
  async connect(apiKey: string, apiSecret: string, testnet: boolean = false): Promise<boolean> {
    try {
      this._apiKey = apiKey
      this._apiSecret = apiSecret
      this.testMode = testnet
      
      // 🌐 自动检测并应用代理配置
      const detectedProxy = await this.autoDetectProxyConfig()
      this.proxyConfig = detectedProxy
      
      if (detectedProxy.enabled) {
        console.log(`🔧 自动应用代理配置: ${detectedProxy.host}:${detectedProxy.port}`)
      } else {
        console.log('🔧 使用直连模式 (未启用代理)')
      }
      
      if (this.testMode) {
        // 测试模式：模拟连接成功
        await new Promise(resolve => setTimeout(resolve, 2000))
        this.isConnected = true
        console.log('✅ 币安API连接成功 (测试模式)')
        return true
      }
      
      // 实际连接逻辑（生产环境）
      // 这里可以添加真实的API连接测试
      this.isConnected = true
      return true
      
    } catch (error) {
      console.error('❌ 币安API连接失败:', error)
      return false
    }
  }

  // 🔧 新增：获取实时API配置（包含代理）
  private async getApiRequestConfig(additionalConfig: any = {}): Promise<any> {
    // 每次请求前都重新检测代理配置，确保使用最新设置
    const currentProxy = await this.autoDetectProxyConfig()
    this.proxyConfig = currentProxy
    
    const baseConfig: any = {
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
        'X-MBX-APIKEY': this._apiKey
      },
      ...additionalConfig
    }
    
    // 应用代理配置
    if (currentProxy.enabled) {
      console.log(`🌐 应用代理配置: ${currentProxy.host}:${currentProxy.port}`)
      baseConfig.proxy = {
        protocol: 'http',
        host: currentProxy.host,
        port: parseInt(currentProxy.port),
        auth: undefined
      }
    }
    
    return baseConfig
  }

  // 设置代理配置
  setProxyConfig(enabled: boolean, host: string = '127.0.0.1', port: string = '7890'): void {
    this.proxyConfig = { enabled, host, port }
    console.log(`🔧 手动设置代理配置: ${enabled ? '启用' : '禁用'} ${host}:${port}`)
  }

  // 获取代理配置
  getProxyConfig(): { enabled: boolean; host: string; port: string } {
    return { ...this.proxyConfig }
  }

  // 获取账户信息
  async getAccountInfo(): Promise<BinanceAccount | null> {
    if (!this.isConnected) return null

    if (this.testMode) {
      // 测试模式：返回基础的空状态，等待真实API配置
      return {
        totalWalletBalance: 0,
        availableBalance: 0,
        unrealizedPnL: 0,
        positions: []
      }
    }

    return null
  }

  // 获取今日交易记录
  async getTodayTrades(): Promise<BinanceTrade[]> {
    if (!this.isConnected) return []

    if (this.testMode) {
      // 测试模式：返回空交易记录，等待真实API配置
      return []
    }

    return []
  }

  // 🔧 增强：获取实时交易指标时检查代理
  async getLiveTradingMetrics(): Promise<LiveTradingMetrics> {
    try {
      // 确保使用最新的代理配置
      const currentProxy = await this.autoDetectProxyConfig()
      
      if (this.testMode) {
        // 测试模式返回模拟数据
        return {
          isConnected: this.isConnected,
          isTrading: true,
          accountBalance: 50000.00,
          availableBalance: 45000.00,
          currentPosition: 2500.00,
          unrealizedPnL: 150.75,
          realizedPnL: 320.50,
          todayTrades: 12,
          currentDrawdown: 5.2,
          maxDrawdownToday: 8.1,
          riskLevel: 'LOW'
        }
      }
      
      // 实际API调用逻辑（使用代理配置）
      // const config = await this.getApiRequestConfig()
      // const response = await fetch('/api/binance/account', config)
      
      return {
        isConnected: this.isConnected,
        isTrading: false,
        accountBalance: 0,
        availableBalance: 0,
        currentPosition: 0,
        unrealizedPnL: 0,
        realizedPnL: 0,
        todayTrades: 0,
        currentDrawdown: 0,
        maxDrawdownToday: 0,
        riskLevel: 'LOW'
      }
      
    } catch (error) {
      console.error('❌ 获取实时交易指标失败:', error)
      return {
        isConnected: false,
        isTrading: false,
        accountBalance: 0,
        availableBalance: 0,
        currentPosition: 0,
        unrealizedPnL: 0,
        realizedPnL: 0,
        todayTrades: 0,
        currentDrawdown: 0,
        maxDrawdownToday: 0,
        riskLevel: 'HIGH'
      }
    }
  }

  // 🔧 增强：启动交易时自动重新检测代理
  async startTrading(): Promise<boolean> {
    try {
      console.log('🚀 启动币安API交易模式...')
      
      // 重新检测代理配置
      const currentProxy = await this.autoDetectProxyConfig()
      this.proxyConfig = currentProxy
      
      if (currentProxy.enabled) {
        console.log(`✅ 交易模式启用代理: ${currentProxy.host}:${currentProxy.port}`)
      } else {
        console.log('✅ 交易模式使用直连')
      }
      
      if (this.testMode) {
        console.log('✅ 币安API交易模式已启动 (测试模式)')
        return true
      }
      
      // 实际启动交易逻辑
      return true
      
    } catch (error) {
      console.error('❌ 启动币安API交易失败:', error)
      return false
    }
  }

  // 停止交易
  async stopTrading(): Promise<boolean> {
    if (!this.isConnected) return false

    if (this.testMode) {
      console.log('策略终极版已停止（测试模式）')
      return true
    }

    // 实际停止策略逻辑
    return false
  }

  // 紧急停止
  async emergencyStop(): Promise<boolean> {
    if (!this.isConnected) return false

    if (this.testMode) {
      console.log('紧急停止执行（测试模式）')
      return true
    }

    // 实际紧急停止逻辑：平仓所有头寸
    return false
  }

  // 获取连接状态
  getConnectionStatus(): boolean {
    return this.isConnected
  }

  // 设置测试模式
  setTestMode(enabled: boolean): void {
    this.testMode = enabled
  }

  // 获取API配置状态（用于调试）
  getApiConfig(): { hasApiKey: boolean; hasSecret: boolean; isConnected: boolean; proxyConfig: any } {
    return {
      hasApiKey: this._apiKey.length > 0,
      hasSecret: this._apiSecret.length > 0,
      isConnected: this.isConnected,
      proxyConfig: this.proxyConfig
    }
  }
}

// 导出单例实例
export const binanceApi = new BinanceApiService() 