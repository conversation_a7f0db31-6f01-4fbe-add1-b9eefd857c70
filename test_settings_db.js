// 🔧 系统设置数据库保存测试脚本
console.log('🧪 启动系统设置数据库保存测试...')

// 测试数据库保存功能
async function testSettingsDbSave() {
  console.log('\n🔬 ===== 系统设置数据库保存测试 =====')
  
  try {
    // 1. 检查服务可用性
    console.log('1️⃣ 检查服务可用性...')
    
    if (typeof strategyStateService === 'undefined') {
      console.error('❌ strategyStateService未加载，请刷新页面')
      return false
    }
    
    console.log('✅ strategyStateService可用')
    
    // 2. 检查当前配置状态
    console.log('\n2️⃣ 检查当前配置状态...')
    
    const dbConfig = await strategyStateService.getApiConfig()
    const localConfig = localStorage.getItem('binance_api_config')
    
    console.log('数据库配置:', dbConfig ? '✅ 存在' : '❌ 不存在')
    console.log('localStorage配置:', localConfig ? '✅ 存在' : '❌ 不存在')
    
    if (dbConfig) {
      console.log('📋 数据库配置详情:')
      console.table({
        'API密钥': `${dbConfig.apiKey.substring(0, 8)}...`,
        '测试网': dbConfig.testnet ? '是' : '否',
        '代理': dbConfig.useProxy ? '是' : '否',
        '创建时间': new Date(dbConfig.createdAt).toLocaleString(),
        '更新时间': new Date(dbConfig.updatedAt).toLocaleString()
      })
    }
    
    // 3. 模拟系统设置保存流程
    console.log('\n3️⃣ 模拟系统设置保存流程...')
    
    const testConfig = {
      apiKey: 'test_api_key_12345678901234567890123456789012345678901234567890123456',
      secretKey: 'test_secret_key_12345678901234567890123456789012345678901234567890123456',
      testnet: true,
      useProxy: true,
      proxyHost: '127.0.0.1',
      proxyPort: '7890'
    }
    
    console.log('💾 测试保存API配置到数据库...')
    const saveSuccess = await strategyStateService.saveApiConfig(testConfig)
    
    if (saveSuccess) {
      console.log('✅ 数据库保存测试成功')
    } else {
      console.error('❌ 数据库保存测试失败')
      return false
    }
    
    // 4. 验证保存结果
    console.log('\n4️⃣ 验证保存结果...')
    
    const savedConfig = await strategyStateService.getApiConfig()
    if (savedConfig) {
      console.log('✅ 配置验证成功')
      console.log('📋 保存的配置:')
      console.table({
        'API密钥匹配': savedConfig.apiKey === testConfig.apiKey ? '✅ 是' : '❌ 否',
        '秘钥匹配': savedConfig.secretKey === testConfig.secretKey ? '✅ 是' : '❌ 否',
        '测试网匹配': savedConfig.testnet === testConfig.testnet ? '✅ 是' : '❌ 否',
        '代理匹配': savedConfig.useProxy === testConfig.useProxy ? '✅ 是' : '❌ 否'
      })
    } else {
      console.error('❌ 配置验证失败，未找到保存的配置')
      return false
    }
    
    // 5. 清理测试数据
    console.log('\n5️⃣ 清理测试数据...')
    
    const cleanupSuccess = await strategyStateService.deleteApiConfig()
    if (cleanupSuccess) {
      console.log('✅ 测试数据清理完成')
    } else {
      console.warn('⚠️ 测试数据清理失败')
    }
    
    console.log('\n🎉 系统设置数据库保存测试完成！')
    return true
    
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error)
    return false
  }
}

// 检查系统设置页面状态
function checkSettingsPageStatus() {
  console.log('\n📊 ===== 系统设置页面状态检查 =====')
  
  try {
    // 检查当前页面
    const currentPath = window.location.hash || window.location.pathname
    console.log('当前页面:', currentPath)
    
    if (!currentPath.includes('settings')) {
      console.warn('⚠️ 当前不在系统设置页面，请导航到设置页面')
      console.log('💡 建议: 点击导航栏的"系统设置"或访问 #/settings')
      return false
    }
    
    // 检查页面元素
    const saveButton = document.querySelector('button[data-testid="save-api-config"], button:contains("保存")')
    console.log('保存按钮:', saveButton ? '✅ 找到' : '❌ 未找到')
    
    const apiKeyInput = document.querySelector('input[placeholder*="API"], input[name*="apiKey"]')
    console.log('API密钥输入框:', apiKeyInput ? '✅ 找到' : '❌ 未找到')
    
    const proxySwitch = document.querySelector('input[type="checkbox"][data-testid*="proxy"], button[role="switch"]')
    console.log('代理开关:', proxySwitch ? '✅ 找到' : '❌ 未找到')
    
    return true
    
  } catch (error) {
    console.error('❌ 页面状态检查失败:', error)
    return false
  }
}

// 检验系统设置的数据库功能
async function verifySettingsDbIntegration() {
  console.log('\n🔍 ===== 验证系统设置数据库集成 =====')
  
  try {
    // 1. 检查页面状态
    if (!checkSettingsPageStatus()) {
      return false
    }
    
    // 2. 检查服务集成
    console.log('\n🔗 检查服务集成...')
    
    if (typeof window.strategyStateService === 'undefined') {
      console.error('❌ window.strategyStateService未定义')
      console.log('💡 可能原因: 页面尚未完全加载或服务未正确导入')
      return false
    }
    
    console.log('✅ strategyStateService已集成到页面')
    
    // 3. 检查导入状态
    console.log('\n📦 检查模块导入状态...')
    
    const hasStrategyStateServiceImport = document.body.innerHTML.includes('strategyStateService') || 
                                          document.documentElement.innerHTML.includes('strategyStateService')
    
    console.log('页面是否包含strategyStateService:', hasStrategyStateServiceImport ? '✅ 是' : '❌ 否')
    
    // 4. 运行数据库测试
    console.log('\n🧪 运行数据库功能测试...')
    const testResult = await testSettingsDbSave()
    
    if (testResult) {
      console.log('\n🎉 ===== 验证完成 =====')
      console.log('✅ 系统设置页面数据库集成验证成功!')
      console.log('💡 现在保存API配置时应该会显示"已成功保存到数据库"')
    } else {
      console.log('\n❌ ===== 验证失败 =====')
      console.log('❌ 系统设置页面数据库集成存在问题')
    }
    
    return testResult
    
  } catch (error) {
    console.error('❌ 验证过程中出现错误:', error)
    return false
  }
}

// 暴露测试函数
window.testSettingsDbSave = testSettingsDbSave
window.checkSettingsPageStatus = checkSettingsPageStatus
window.verifySettingsDbIntegration = verifySettingsDbIntegration

console.log(`
🔧 可用的测试命令:
• testSettingsDbSave()          - 测试数据库保存功能
• checkSettingsPageStatus()     - 检查设置页面状态
• verifySettingsDbIntegration() - 完整验证数据库集成

建议使用顺序:
1. verifySettingsDbIntegration() - 完整验证
2. 如有问题，使用其他命令单独测试

使用方法:
在浏览器控制台输入命令并按回车执行
`)

// 自动运行验证
console.log('\n🚀 开始自动验证...')
setTimeout(() => {
  verifySettingsDbIntegration()
}, 2000) // 等待2秒确保页面完全加载 