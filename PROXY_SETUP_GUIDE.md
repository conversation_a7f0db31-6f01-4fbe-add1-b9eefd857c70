# 🌐 Clash代理自动检测配置指南

## 概述

系统现在支持自动检测Clash代理配置，当你在系统设置中启用代理时，所有币安API调用都会自动通过代理进行。

## 🔧 配置步骤

### 1. 启动Clash代理
- 确保Clash应用正在运行
- 确认HTTP代理端口为 `7890`
- 确认处于代理模式（非直连模式）

### 2. 在系统中启用代理
1. 进入"系统设置" → "风险管理"
2. 勾选 ✅ "使用代理"
3. 设置代理地址：`127.0.0.1`
4. 设置代理端口：`7890`
5. 点击"保存配置"

### 3. 验证代理配置
在浏览器控制台运行以下脚本：

```javascript
// 加载代理检测脚本
const script = document.createElement('script');
script.src = './proxy_detection_test.js';
document.head.appendChild(script);
```

然后运行：

```javascript
// 检测代理状态
detectProxyStatus()

// 自动修复代理配置
autoFixProxyConfig()
```

## 🚀 工作原理

### 自动代理检测
1. **优先级检查**：系统按以下顺序检查代理配置
   - 数据库中的API配置（IndexedDB）
   - localStorage中的API配置
   - 默认设置（直连）

2. **实时应用**：每次API调用前都会重新检测代理配置
   - 确保使用最新的代理设置
   - 支持动态切换代理/直连模式

3. **统一管理**：代理配置同时保存在
   - IndexedDB数据库（持久化存储）
   - localStorage（向后兼容）
   - binanceApi服务实例

### 自动应用场景
系统会在以下情况自动检测并应用代理：

- ✅ binanceApi.connect() - 连接币安API时
- ✅ binanceApi.startTrading() - 启动交易时
- ✅ binanceApi.getLiveTradingMetrics() - 获取实时数据时
- ✅ 所有币安API调用前

## 🔍 诊断工具

### 1. 代理状态检测
```javascript
detectProxyStatus()
```
显示：
- localStorage代理配置
- 数据库代理配置  
- binanceApi当前代理状态
- 配置一致性检查

### 2. 网络连接测试
```javascript
testNetworkConnection()
```
测试：
- 币安现货API连通性
- 币安期货API连通性
- 当前IP地址（判断是否通过代理）

### 3. Clash代理检查
```javascript
checkClashProxy()
```
提供Clash代理检查清单

### 4. 自动修复配置
```javascript
autoFixProxyConfig()
```
自动检测并修复代理配置问题

### 5. 强制应用Clash代理
```javascript
forceUpdateProxyConfig()
```
强制设置为标准Clash代理配置 (127.0.0.1:7890)

## ⚠️ 常见问题

### Q1: 代理不生效怎么办？
**解决方案：**
1. 确认Clash正在运行且端口为7890
2. 运行 `autoFixProxyConfig()` 自动修复
3. 检查系统设置中代理配置是否正确

### Q2: 网络连接失败
**可能原因：**
- Clash未启动
- 代理端口不正确
- 代理模式未开启

**解决方案：**
1. 重启Clash应用
2. 确认端口设置为7890
3. 切换为代理模式（非直连）

### Q3: 配置不一致
**现象：** localStorage和数据库代理设置不同

**解决方案：**
```javascript
// 运行自动修复
autoFixProxyConfig()
```

### Q4: 如何验证代理是否生效？
```javascript
// 检查当前IP
testNetworkConnection()
```
如果显示的IP地址不是你的真实IP，说明代理生效。

## 📋 Clash检查清单

### 必须确认的项目：
- [ ] Clash应用正在运行
- [ ] HTTP代理端口设置为7890
- [ ] 处于代理模式（非Direct直连）
- [ ] 有可用的代理节点
- [ ] 系统设置中已启用代理

### 推荐设置：
- **代理主机**: `127.0.0.1`
- **代理端口**: `7890`
- **代理类型**: HTTP代理
- **认证**: 无需认证

## 🎯 最佳实践

1. **启动策略前**：运行 `detectProxyStatus()` 确认代理状态
2. **遇到连接问题**：运行 `autoFixProxyConfig()` 自动修复
3. **定期检查**：每天启动时检查Clash代理状态
4. **网络切换后**：重新检查代理配置是否正确

## 🚨 重要提醒

- 在国内使用币安API **必须** 通过代理
- 代理配置错误会导致API连接失败
- 系统会自动检测并应用最新的代理配置
- 代理设置保存在数据库中，清除浏览器缓存不会丢失

## 📞 技术支持

如果遇到代理配置问题，请：
1. 运行完整诊断：`diagnoseStartup()`
2. 检查代理状态：`detectProxyStatus()`
3. 尝试自动修复：`autoFixProxyConfig()`
4. 提供诊断结果以获得支持 