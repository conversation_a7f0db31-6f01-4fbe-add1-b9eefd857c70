// 🔧 API配置迁移脚本
console.log('🔑 启动API配置迁移工具...')

// 手动迁移API配置到数据库
async function migrateApiToDatabase() {
  console.log('\n📋 ===== API配置迁移工具 =====')
  
  try {
    // 1. 检查数据库服务是否可用
    if (typeof strategyStateService === 'undefined') {
      console.error('❌ 策略状态服务未加载，请刷新页面后重试')
      return false
    }
    
    // 2. 检查localStorage中的API配置
    const localStorageConfig = localStorage.getItem('binance_api_config')
    if (!localStorageConfig) {
      console.warn('⚠️ localStorage中没有API配置')
      return false
    }
    
    let config
    try {
      config = JSON.parse(localStorageConfig)
      console.log('✅ 发现localStorage API配置')
    } catch (error) {
      console.error('❌ localStorage API配置格式错误:', error)
      return false
    }
    
    // 3. 验证API配置完整性
    const requiredFields = ['apiKey', 'secretKey']
    const missingFields = requiredFields.filter(field => !config[field])
    
    if (missingFields.length > 0) {
      console.error('❌ API配置不完整，缺少字段:', missingFields)
      return false
    }
    
    console.log('📋 当前API配置:')
    console.table({
      'API密钥': config.apiKey ? `${config.apiKey.substring(0, 8)}...` : '未设置',
      '测试网模式': config.testnet ? '是' : '否',
      '使用代理': config.useProxy ? '是' : '否',
      '代理地址': config.useProxy ? `${config.proxyHost || '127.0.0.1'}:${config.proxyPort || '7890'}` : '未配置'
    })
    
    // 4. 检查数据库中是否已有配置
    console.log('🔍 检查数据库中的现有配置...')
    const existingConfig = await strategyStateService.getApiConfig()
    
    if (existingConfig) {
      console.log('ℹ️ 数据库中已有API配置:')
      console.table({
        '创建时间': new Date(existingConfig.createdAt).toLocaleString(),
        '更新时间': new Date(existingConfig.updatedAt).toLocaleString(),
        'API密钥': `${existingConfig.apiKey.substring(0, 8)}...`,
        '测试网模式': existingConfig.testnet ? '是' : '否'
      })
      
      const shouldOverwrite = confirm('数据库中已有API配置，是否覆盖？')
      if (!shouldOverwrite) {
        console.log('⚪ 用户取消覆盖，迁移中止')
        return false
      }
    }
    
    // 5. 迁移API配置到数据库
    console.log('💾 开始迁移API配置到数据库...')
    
    const migrationConfig = {
      apiKey: config.apiKey,
      secretKey: config.secretKey,
      testnet: config.testnet || false,
      useProxy: config.useProxy || false,
      proxyHost: config.proxyHost || '127.0.0.1',
      proxyPort: config.proxyPort || '7890'
    }
    
    const success = await strategyStateService.saveApiConfig(migrationConfig)
    
    if (success) {
      console.log('✅ API配置迁移成功！')
      console.log('🔧 API配置现在保存在数据库中，不会因为清除浏览器缓存而丢失')
      
      // 验证迁移结果
      const migratedConfig = await strategyStateService.getApiConfig()
      if (migratedConfig) {
        console.log('✅ 迁移验证成功')
        console.log(`📅 配置时间: ${new Date(migratedConfig.createdAt).toLocaleString()}`)
      }
      
      return true
    } else {
      console.error('❌ API配置迁移失败')
      return false
    }
    
  } catch (error) {
    console.error('❌ 迁移过程中发生错误:', error)
    return false
  }
}

// 查看当前API配置状态
async function checkApiConfigStatus() {
  console.log('\n📊 ===== API配置状态检查 =====')
  
  try {
    // 检查localStorage
    const localStorageConfig = localStorage.getItem('binance_api_config')
    console.log('localStorage API配置:', localStorageConfig ? '✅ 存在' : '❌ 不存在')
    
    if (localStorageConfig) {
      try {
        const config = JSON.parse(localStorageConfig)
        console.log('localStorage配置详情:')
        console.table({
          'API密钥': config.apiKey ? `${config.apiKey.substring(0, 8)}...` : '未设置',
          '测试网': config.testnet ? '是' : '否',
          '代理': config.useProxy ? '是' : '否'
        })
      } catch (error) {
        console.error('❌ localStorage配置格式错误')
      }
    }
    
    // 检查数据库
    if (typeof strategyStateService !== 'undefined') {
      const dbConfig = await strategyStateService.getApiConfig()
      console.log('数据库API配置:', dbConfig ? '✅ 存在' : '❌ 不存在')
      
      if (dbConfig) {
        console.log('数据库配置详情:')
        console.table({
          '创建时间': new Date(dbConfig.createdAt).toLocaleString(),
          '更新时间': new Date(dbConfig.updatedAt).toLocaleString(),
          'API密钥': `${dbConfig.apiKey.substring(0, 8)}...`,
          '测试网': dbConfig.testnet ? '是' : '否',
          '代理': dbConfig.useProxy ? '是' : '否'
        })
      }
    } else {
      console.error('❌ 策略状态服务未加载')
    }
    
  } catch (error) {
    console.error('❌ 检查API配置状态失败:', error)
  }
}

// 清除API配置
async function clearApiConfig() {
  console.log('\n🗑️ ===== 清除API配置 =====')
  
  const shouldClear = confirm('确定要清除所有API配置吗？这将删除localStorage和数据库中的配置。')
  if (!shouldClear) {
    console.log('⚪ 用户取消清除操作')
    return
  }
  
  try {
    // 清除localStorage
    localStorage.removeItem('binance_api_config')
    console.log('✅ localStorage API配置已清除')
    
    // 清除数据库
    if (typeof strategyStateService !== 'undefined') {
      const success = await strategyStateService.deleteApiConfig()
      if (success) {
        console.log('✅ 数据库API配置已清除')
      } else {
        console.error('❌ 数据库API配置清除失败')
      }
    }
    
    console.log('🎉 所有API配置已清除完成')
    
  } catch (error) {
    console.error('❌ 清除API配置失败:', error)
  }
}

// 暴露工具函数
window.migrateApiToDatabase = migrateApiToDatabase
window.checkApiConfigStatus = checkApiConfigStatus
window.clearApiConfig = clearApiConfig

console.log(`
🔧 可用的API配置管理命令:
• migrateApiToDatabase()  - 迁移API配置到数据库
• checkApiConfigStatus()  - 检查API配置状态
• clearApiConfig()        - 清除所有API配置

建议运行: checkApiConfigStatus()
然后运行: migrateApiToDatabase()
`)

// 自动检查API配置状态
checkApiConfigStatus() 