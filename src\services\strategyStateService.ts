// 策略状态服务 - 将策略状态持久化到数据库
// 解决localStorage被清除导致状态丢失的问题

export interface StrategyConfig {
  id: string
  symbol: string                  // 交易币种
  isActive: boolean              // 是否激活
  createdAt: number              // 创建时间
  updatedAt: number              // 更新时间
  
  // 策略参数
  parameters: {
    capitalRatio: number         // 资金使用比例
    initialCapital: number       // 初始资金
    maxSingleTrade: number       // 单笔最大交易
    scalpingWeight: number       // 超短线权重
    trendWeight: number          // 趋势策略权重
    gridWeight: number           // 网格策略权重
    superTrendWeight: number     // 超强趋势权重
    gridSpacing: number          // 网格间距
    scalpProfit: number          // 超短线止盈
    trendFastProfit: number      // 趋势快速止盈
  }
  
  // 风险配置
  riskConfig: {
    maxDrawdownLimit: number     // 最大回撤限制
    maxPositionRatio: number     // 最大仓位比例
    profitProtectionLine: number // 利润保护线
    maxLeverageRisk: number      // 最大杠杆风险
  }
  
  // 运行时状态
  runtime: {
    startedAt?: number           // 启动时间
    stoppedAt?: number           // 停止时间
    lastHeartbeat: number        // 最后心跳时间
    currentStatus: 'active' | 'inactive' | 'paused' | 'error' | 'emergency_stopped'
    errorReason?: string         // 错误原因
    totalTrades: number          // 总交易次数
    realizedPnl: number          // 已实现盈亏
    unrealizedPnl: number        // 未实现盈亏
  }
}

// API配置接口
export interface ApiConfig {
  id: string
  apiKey: string
  secretKey: string
  testnet: boolean
  useProxy: boolean
  proxyHost: string
  proxyPort: string
  createdAt: number
  updatedAt: number
}

// 系统配置
export interface SystemConfig {
  id: string
  version: string
  lastBackup: number
  configurations: {
    [key: string]: any
  }
}

class StrategyStateService {
  private db: IDBDatabase | null = null
  private readonly DB_NAME = 'QuantStrategyStateDB'
  private readonly DB_VERSION = 2  // 增加版本号以触发升级，创建api_configs表
  private heartbeatInterval: NodeJS.Timeout | null = null
  private currentConfig: StrategyConfig | null = null

  constructor() {
    console.log('🔧 初始化策略状态服务...')
    this.initDatabase().catch(error => {
      console.error('❌ 数据库初始化失败:', error)
    })
  }

  // 🔧 公开的初始化方法（用于修复时重新初始化）
  async initializeDatabase(): Promise<void> {
    return this.initDatabase()
  }

  // 初始化数据库
  private async initDatabase(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.DB_NAME, this.DB_VERSION)
      
      request.onerror = () => {
        console.error('❌ 策略状态数据库连接失败:', request.error)
        reject(request.error)
      }
      
      request.onsuccess = () => {
        this.db = request.result
        console.log('✅ 策略状态数据库连接成功')
        this.loadActiveStrategy()
        // 迁移localStorage中的API配置
        this.migrateApiConfigFromLocalStorage()
        resolve()
      }
      
      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result
        const oldVersion = event.oldVersion
        const newVersion = event.newVersion
        
        console.log(`🔄 数据库升级中... 从版本 ${oldVersion} 到版本 ${newVersion}`)
        
        // 创建策略配置表
        if (!db.objectStoreNames.contains('strategy_configs')) {
          const configStore = db.createObjectStore('strategy_configs', { keyPath: 'id' })
          configStore.createIndex('symbol', 'symbol', { unique: false })
          configStore.createIndex('isActive', 'isActive', { unique: false })
          configStore.createIndex('updatedAt', 'updatedAt', { unique: false })
          console.log('📊 创建策略配置表')
        }
        
        // 创建系统配置表
        if (!db.objectStoreNames.contains('system_configs')) {
          const systemStore = db.createObjectStore('system_configs', { keyPath: 'id' })
          console.log('⚙️ 创建系统配置表')
        }
        
        // 🔧 版本2新增：创建API配置表
        if (!db.objectStoreNames.contains('api_configs')) {
          const apiStore = db.createObjectStore('api_configs', { keyPath: 'id' })
          apiStore.createIndex('updatedAt', 'updatedAt', { unique: false })
          console.log('🔑 创建API配置表')
        }
        
        console.log(`🚀 数据库升级完成 v${newVersion}，所有表结构已创建`)
        console.log('📊 当前数据库表列表:', Array.from(db.objectStoreNames))
      }
    })
  }

  // 保存策略配置并激活
  async saveAndActivateStrategy(config: Omit<StrategyConfig, 'id' | 'createdAt' | 'updatedAt'>): Promise<boolean> {
    console.log('💾 开始保存策略配置...')
    console.log('📋 配置详情:', config)
    
    if (!this.db) {
      console.error('❌ 数据库未连接，等待初始化...')
      
      // 等待数据库初始化
      let retries = 0
      while (!this.db && retries < 10) {
        await new Promise(resolve => setTimeout(resolve, 500))
        retries++
        console.log(`⏳ 等待数据库初始化... (${retries}/10)`)
      }
      
      if (!this.db) {
        console.error('❌ 数据库初始化超时')
        return false
      }
    }

    try {
      const now = Date.now()
      
      console.log('🛑 停用现有策略...')
      // 首先停用所有现有策略
      await this.deactivateAllStrategies()
      
      // 创建新的策略配置
      const strategyConfig: StrategyConfig = {
        ...config,
        id: `strategy_${config.symbol}_${now}`,
        createdAt: now,
        updatedAt: now,
        isActive: true,
        runtime: {
          ...config.runtime,
          startedAt: now,
          lastHeartbeat: now,
          currentStatus: 'active'
        }
      }

      console.log('💾 保存新策略配置:', strategyConfig)

      const transaction = this.db.transaction(['strategy_configs'], 'readwrite')
      const store = transaction.objectStore('strategy_configs')
      
      await new Promise<void>((resolve, reject) => {
        const request = store.add(strategyConfig)
        request.onsuccess = () => {
          console.log(`✅ 策略配置已保存并激活: ${strategyConfig.symbol}`)
          console.log(`📊 策略ID: ${strategyConfig.id}`)
          console.log(`🎯 交易币种: ${strategyConfig.symbol}`)
          console.log(`💰 初始资金: $${strategyConfig.parameters.initialCapital}`)
          resolve()
        }
        request.onerror = () => {
          console.error('❌ 数据库写入失败:', request.error)
          reject(request.error)
        }
      })

      this.currentConfig = strategyConfig
      
      // 启动心跳机制
      console.log('💓 启动心跳机制...')
      this.startHeartbeat()
      
      // 同步到localStorage (临时兼容性)
      localStorage.setItem('trading_status', 'active')
      localStorage.setItem('selected_symbol', strategyConfig.symbol)
      
      console.log('✅ 策略配置保存完成')
      return true
      
    } catch (error) {
      console.error('❌ 保存策略配置失败:', error)
      console.error('错误详情:', error)
      return false
    }
  }

  // 停用策略
  async deactivateStrategy(reason?: string): Promise<boolean> {
    if (!this.currentConfig) {
      console.log('⚪ 没有活跃的策略需要停用')
      return true
    }

    try {
      const now = Date.now()
      
      // 更新数据库中的策略状态
      const updatedConfig: StrategyConfig = {
        ...this.currentConfig,
        isActive: false,
        updatedAt: now,
        runtime: {
          ...this.currentConfig.runtime,
          stoppedAt: now,
          currentStatus: reason ? 'error' : 'inactive',
          errorReason: reason,
          lastHeartbeat: now
        }
      }

      await this.updateStrategyConfig(updatedConfig)
      
      // 停止心跳
      this.stopHeartbeat()
      
      // 同步到localStorage (临时兼容性)
      localStorage.setItem('trading_status', 'inactive')
      
      console.log(`✅ 策略已停用: ${this.currentConfig.symbol}`)
      if (reason) {
        console.log(`❌ 停用原因: ${reason}`)
      }
      
      this.currentConfig = null
      return true
      
    } catch (error) {
      console.error('❌ 停用策略失败:', error)
      return false
    }
  }

  // 获取当前活跃策略
  async getActiveStrategy(): Promise<StrategyConfig | null> {
    if (this.currentConfig && this.currentConfig.isActive) {
      return this.currentConfig
    }

    if (!this.db) return null

    try {
      const transaction = this.db.transaction(['strategy_configs'], 'readonly')
      const store = transaction.objectStore('strategy_configs')
      const index = store.index('isActive')
      
      const activeConfigs = await new Promise<StrategyConfig[]>((resolve, reject) => {
        const request = index.getAll(IDBKeyRange.only(true))
        request.onsuccess = () => resolve(request.result)
        request.onerror = () => reject(request.error)
      })

      if (activeConfigs.length > 0) {
        // 取最新的活跃策略
        const latestConfig = activeConfigs.sort((a, b) => b.updatedAt - a.updatedAt)[0]
        
        // 检查心跳是否过期 (超过5分钟无心跳认为已离线)
        const heartbeatTimeout = 5 * 60 * 1000
        if (Date.now() - latestConfig.runtime.lastHeartbeat > heartbeatTimeout) {
          console.warn(`⚠️ 策略心跳超时，自动标记为离线: ${latestConfig.symbol}`)
          await this.markStrategyOffline(latestConfig.id)
          return null
        }
        
        this.currentConfig = latestConfig
        this.startHeartbeat() // 重新启动心跳
        return latestConfig
      }

      return null
      
    } catch (error) {
      console.error('❌ 获取活跃策略失败:', error)
      return null
    }
  }

  // 更新策略运行时状态
  async updateRuntimeStatus(updates: Partial<StrategyConfig['runtime']>): Promise<boolean> {
    if (!this.currentConfig) return false

    try {
      const now = Date.now()
      const updatedConfig: StrategyConfig = {
        ...this.currentConfig,
        updatedAt: now,
        runtime: {
          ...this.currentConfig.runtime,
          ...updates,
          lastHeartbeat: now
        }
      }

      await this.updateStrategyConfig(updatedConfig)
      this.currentConfig = updatedConfig
      return true
      
    } catch (error) {
      console.error('❌ 更新策略运行时状态失败:', error)
      return false
    }
  }

  // 加载活跃策略 (系统启动时调用)
  private async loadActiveStrategy(): Promise<void> {
    try {
      const activeStrategy = await this.getActiveStrategy()
      if (activeStrategy) {
        console.log('🔄 检测到活跃策略，恢复状态:', {
          symbol: activeStrategy.symbol,
          startedAt: new Date(activeStrategy.runtime.startedAt || 0).toLocaleString(),
          totalTrades: activeStrategy.runtime.totalTrades,
          status: activeStrategy.runtime.currentStatus
        })
        
        // 同步到localStorage (临时兼容性)
        localStorage.setItem('trading_status', 'active')
        localStorage.setItem('selected_symbol', activeStrategy.symbol)
        
        // 启动心跳
        this.startHeartbeat()
      } else {
        console.log('⚪ 未检测到活跃策略')
        // 清理localStorage
        localStorage.setItem('trading_status', 'inactive')
      }
    } catch (error) {
      console.error('❌ 加载活跃策略失败:', error)
    }
  }

  // 停用所有策略 (内部方法)
  private async deactivateAllStrategies(): Promise<void> {
    if (!this.db) return

    const transaction = this.db.transaction(['strategy_configs'], 'readwrite')
    const store = transaction.objectStore('strategy_configs')
    const index = store.index('isActive')
    
    await new Promise<void>((resolve, reject) => {
      const request = index.openCursor(IDBKeyRange.only(true))
      request.onsuccess = () => {
        const cursor = request.result
        if (cursor) {
          const config = cursor.value as StrategyConfig
          config.isActive = false
          config.updatedAt = Date.now()
          config.runtime.currentStatus = 'inactive'
          config.runtime.stoppedAt = Date.now()
          
          cursor.update(config)
          cursor.continue()
        } else {
          resolve()
        }
      }
      request.onerror = () => reject(request.error)
    })
  }

  // 更新策略配置 (内部方法)
  private async updateStrategyConfig(config: StrategyConfig): Promise<void> {
    if (!this.db) return

    const transaction = this.db.transaction(['strategy_configs'], 'readwrite')
    const store = transaction.objectStore('strategy_configs')
    
    await new Promise<void>((resolve, reject) => {
      const request = store.put(config)
      request.onsuccess = () => resolve()
      request.onerror = () => reject(request.error)
    })
  }

  // 标记策略离线 (内部方法)
  private async markStrategyOffline(strategyId: string): Promise<void> {
    if (!this.db) return

    const transaction = this.db.transaction(['strategy_configs'], 'readwrite')
    const store = transaction.objectStore('strategy_configs')
    
    const config = await new Promise<StrategyConfig>((resolve, reject) => {
      const request = store.get(strategyId)
      request.onsuccess = () => resolve(request.result)
      request.onerror = () => reject(request.error)
    })

    if (config) {
      config.isActive = false
      config.runtime.currentStatus = 'error'
      config.runtime.errorReason = '心跳超时，策略已离线'
      config.updatedAt = Date.now()
      
      await this.updateStrategyConfig(config)
    }
  }

  // 启动心跳机制
  private startHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
    }
    
    this.heartbeatInterval = setInterval(async () => {
      if (this.currentConfig) {
        await this.updateRuntimeStatus({
          lastHeartbeat: Date.now()
        })
      }
    }, 30000) // 每30秒发送一次心跳
    
    console.log('💓 策略心跳已启动')
  }

  // 停止心跳机制
  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
      console.log('💔 策略心跳已停止')
    }
  }

  // 获取所有策略历史
  async getStrategyHistory(limit: number = 10): Promise<StrategyConfig[]> {
    if (!this.db) return []

    try {
      const transaction = this.db.transaction(['strategy_configs'], 'readonly')
      const store = transaction.objectStore('strategy_configs')
      const index = store.index('updatedAt')
      
      const configs = await new Promise<StrategyConfig[]>((resolve, reject) => {
        const request = index.getAll()
        request.onsuccess = () => resolve(request.result)
        request.onerror = () => reject(request.error)
      })

      return configs
        .sort((a, b) => b.updatedAt - a.updatedAt)
        .slice(0, limit)
      
    } catch (error) {
      console.error('❌ 获取策略历史失败:', error)
      return []
    }
  }

  // 清理旧的策略配置
  async cleanupOldConfigs(days: number = 30): Promise<void> {
    const cutoffTime = Date.now() - (days * 24 * 60 * 60 * 1000)
    
    if (!this.db) return

    try {
      const transaction = this.db.transaction(['strategy_configs'], 'readwrite')
      const store = transaction.objectStore('strategy_configs')
      const index = store.index('updatedAt')
      const range = IDBKeyRange.upperBound(cutoffTime)
      
      await new Promise<void>((resolve, reject) => {
        const request = index.openCursor(range)
        request.onsuccess = () => {
          const cursor = request.result
          if (cursor) {
            const config = cursor.value as StrategyConfig
            // 只删除非活跃的旧配置
            if (!config.isActive) {
              cursor.delete()
            }
            cursor.continue()
          } else {
            resolve()
          }
        }
        request.onerror = () => reject(request.error)
      })
      
      console.log(`✅ 清理了${days}天前的旧策略配置`)
      
    } catch (error) {
      console.error('❌ 清理旧策略配置失败:', error)
    }
  }

  // 获取当前策略状态 (对外接口)
  getCurrentStrategy(): StrategyConfig | null {
    return this.currentConfig
  }

  // 检查是否有活跃策略 (对外接口)
  hasActiveStrategy(): boolean {
    return this.currentConfig !== null && this.currentConfig.isActive
  }

  // 获取策略运行状态 (对外接口)
  getStrategyStatus(): 'active' | 'inactive' | 'paused' | 'error' | 'emergency_stopped' {
    if (!this.currentConfig) return 'inactive'
    return this.currentConfig.runtime.currentStatus
  }

  // 停用所有策略 (对外接口)
  async stopAllStrategies(): Promise<void> {
    await this.deactivateAllStrategies()
    
    // 停止心跳
    this.stopHeartbeat()
    
    // 清理当前配置
    this.currentConfig = null
    
    console.log('✅ 所有策略已停用')
  }

  // ===== API配置管理 =====

  // 保存API配置到数据库
  async saveApiConfig(config: Omit<ApiConfig, 'id' | 'createdAt' | 'updatedAt'>): Promise<boolean> {
    console.log('🔑 保存API配置到数据库...')
    
    if (!this.db) {
      console.error('❌ 数据库未连接')
      return false
    }

    try {
      const now = Date.now()
      
      // 先删除现有的API配置（保持单一配置）
      await this.clearApiConfigs()
      
      const apiConfig: ApiConfig = {
        ...config,
        id: `api_config_${now}`,
        createdAt: now,
        updatedAt: now
      }

      const transaction = this.db.transaction(['api_configs'], 'readwrite')
      const store = transaction.objectStore('api_configs')
      
      await new Promise<void>((resolve, reject) => {
        const request = store.add(apiConfig)
        request.onsuccess = () => {
          console.log('✅ API配置已保存到数据库')
          console.log(`🔐 API密钥: ${apiConfig.apiKey.substring(0, 8)}...`)
          console.log(`🌐 测试网模式: ${apiConfig.testnet ? '是' : '否'}`)
          console.log(`🔧 使用代理: ${apiConfig.useProxy ? '是' : '否'}`)
          resolve()
        }
        request.onerror = () => {
          console.error('❌ API配置保存失败:', request.error)
          reject(request.error)
        }
      })

      // 同步到localStorage (向后兼容)
      const legacyConfig = {
        apiKey: config.apiKey,
        secretKey: config.secretKey,
        testnet: config.testnet,
        useProxy: config.useProxy,
        proxyHost: config.proxyHost,
        proxyPort: config.proxyPort
      }
      localStorage.setItem('binance_api_config', JSON.stringify(legacyConfig))
      
      console.log('✅ API配置已同步到localStorage (兼容模式)')
      return true
      
    } catch (error) {
      console.error('❌ 保存API配置失败:', error)
      return false
    }
  }

  // 从数据库获取API配置
  async getApiConfig(): Promise<ApiConfig | null> {
    if (!this.db) return null

    try {
      const transaction = this.db.transaction(['api_configs'], 'readonly')
      const store = transaction.objectStore('api_configs')
      
      const configs = await new Promise<ApiConfig[]>((resolve, reject) => {
        const request = store.getAll()
        request.onsuccess = () => resolve(request.result)
        request.onerror = () => reject(request.error)
      })

      if (configs.length > 0) {
        // 返回最新的配置
        const latestConfig = configs.sort((a, b) => b.updatedAt - a.updatedAt)[0]
        console.log('🔑 从数据库恢复API配置')
        return latestConfig
      }

      return null
      
    } catch (error) {
      console.error('❌ 获取API配置失败:', error)
      return null
    }
  }

  // 更新API配置
  async updateApiConfig(updates: Partial<Omit<ApiConfig, 'id' | 'createdAt'>>): Promise<boolean> {
    const currentConfig = await this.getApiConfig()
    if (!currentConfig) {
      console.error('❌ 没有找到现有API配置')
      return false
    }

    const updatedConfig = {
      ...currentConfig,
      ...updates,
      updatedAt: Date.now()
    }

    if (!this.db) return false

    try {
      const transaction = this.db.transaction(['api_configs'], 'readwrite')
      const store = transaction.objectStore('api_configs')
      
      await new Promise<void>((resolve, reject) => {
        const request = store.put(updatedConfig)
        request.onsuccess = () => {
          console.log('✅ API配置已更新')
          resolve()
        }
        request.onerror = () => reject(request.error)
      })

      // 同步到localStorage
      const legacyConfig = {
        apiKey: updatedConfig.apiKey,
        secretKey: updatedConfig.secretKey,
        testnet: updatedConfig.testnet,
        useProxy: updatedConfig.useProxy,
        proxyHost: updatedConfig.proxyHost,
        proxyPort: updatedConfig.proxyPort
      }
      localStorage.setItem('binance_api_config', JSON.stringify(legacyConfig))
      
      return true
      
    } catch (error) {
      console.error('❌ 更新API配置失败:', error)
      return false
    }
  }

  // 删除API配置
  async deleteApiConfig(): Promise<boolean> {
    if (!this.db) return false

    try {
      const transaction = this.db.transaction(['api_configs'], 'readwrite')
      const store = transaction.objectStore('api_configs')
      
      await new Promise<void>((resolve, reject) => {
        const request = store.clear()
        request.onsuccess = () => {
          console.log('✅ API配置已从数据库删除')
          resolve()
        }
        request.onerror = () => reject(request.error)
      })

      // 清除localStorage
      localStorage.removeItem('binance_api_config')
      
      return true
      
    } catch (error) {
      console.error('❌ 删除API配置失败:', error)
      return false
    }
  }

  // 清除所有API配置 (内部方法)
  private async clearApiConfigs(): Promise<void> {
    if (!this.db) return

    const transaction = this.db.transaction(['api_configs'], 'readwrite')
    const store = transaction.objectStore('api_configs')
    
    await new Promise<void>((resolve, reject) => {
      const request = store.clear()
      request.onsuccess = () => resolve()
      request.onerror = () => reject(request.error)
    })
  }

  // 从localStorage迁移API配置到数据库
  async migrateApiConfigFromLocalStorage(): Promise<boolean> {
    const localStorageConfig = localStorage.getItem('binance_api_config')
    
    if (!localStorageConfig) {
      console.log('⚪ localStorage中没有API配置需要迁移')
      return false
    }

    try {
      const config = JSON.parse(localStorageConfig)
      
      // 检查数据库中是否已有配置
      const existingConfig = await this.getApiConfig()
      if (existingConfig) {
        console.log('ℹ️ 数据库中已有API配置，跳过迁移')
        return true
      }

      // 迁移配置到数据库
      const success = await this.saveApiConfig({
        apiKey: config.apiKey || '',
        secretKey: config.secretKey || '',
        testnet: config.testnet || false,
        useProxy: config.useProxy || false,
        proxyHost: config.proxyHost || '127.0.0.1',
        proxyPort: config.proxyPort || '7890'
      })

      if (success) {
        console.log('✅ API配置已从localStorage迁移到数据库')
      }

      return success
      
    } catch (error) {
      console.error('❌ API配置迁移失败:', error)
      return false
    }
  }
}

// 创建全局实例
export const strategyStateService = new StrategyStateService()

// 导出默认实例
export default strategyStateService 