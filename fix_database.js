// 🔧 数据库修复脚本
console.log('🛠️ 启动数据库修复工具...')

// 清理并重建数据库
async function fixDatabase() {
  console.log('\n🗑️ ===== 数据库修复流程 =====')
  
  try {
    // 1. 备份现有数据
    console.log('1️⃣ 备份现有数据...')
    
    let backupData = {
      localStorage: {},
      strategyConfigs: [],
      apiConfigs: []
    }
    
    // 备份localStorage
    const keys = ['binance_api_config', 'trading_status', 'selected_symbol', 'strategy_config']
    keys.forEach(key => {
      const value = localStorage.getItem(key)
      if (value) {
        backupData.localStorage[key] = value
        console.log(`💾 备份localStorage[${key}]`)
      }
    })
    
    // 尝试备份现有数据库数据
    try {
      if (typeof strategyStateService !== 'undefined') {
        const configs = await strategyStateService.getStrategyHistory(50)
        backupData.strategyConfigs = configs || []
        console.log(`💾 备份策略配置: ${configs.length} 条`)
        
        const apiConfig = await strategyStateService.getApiConfig()
        if (apiConfig) {
          backupData.apiConfigs = [apiConfig]
          console.log(`💾 备份API配置: 1 条`)
        }
      }
    } catch (backupError) {
      console.warn('⚠️ 数据库数据备份失败（这是正常的，因为数据库结构损坏）:', backupError.message)
    }
    
    // 2. 删除损坏的数据库
    console.log('\n2️⃣ 删除损坏的数据库...')
    
    const dbName = 'QuantStrategyStateDB'
    
    await new Promise((resolve, reject) => {
      const deleteRequest = indexedDB.deleteDatabase(dbName)
      
      deleteRequest.onsuccess = () => {
        console.log('✅ 旧数据库已删除')
        resolve(true)
      }
      
      deleteRequest.onerror = () => {
        console.error('❌ 删除数据库失败:', deleteRequest.error)
        reject(deleteRequest.error)
      }
      
      deleteRequest.onblocked = () => {
        console.warn('⚠️ 数据库删除被阻塞，请关闭其他标签页后重试')
        alert('请关闭其他标签页后重试数据库修复')
        reject(new Error('数据库删除被阻塞'))
      }
    })
    
    // 3. 等待确保数据库完全删除
    console.log('3️⃣ 等待数据库删除完成...')
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 4. 重新初始化数据库
    console.log('\n4️⃣ 重新初始化数据库...')
    
    // 强制重新创建strategyStateService实例
    if (typeof strategyStateService !== 'undefined') {
      console.log('🔄 重新初始化策略状态服务...')
      // 等待数据库重新初始化
      await strategyStateService.initializeDatabase()
    }
    
    // 5. 验证新数据库结构
    console.log('\n5️⃣ 验证新数据库结构...')
    
    await new Promise((resolve, reject) => {
      const request = indexedDB.open(dbName, 2)
      
      request.onsuccess = () => {
        const db = request.result
        console.log('✅ 新数据库连接成功')
        console.log('📊 数据库表列表:', Array.from(db.objectStoreNames))
        
        const expectedTables = ['strategy_configs', 'system_configs', 'api_configs']
        const missingTables = expectedTables.filter(table => !db.objectStoreNames.contains(table))
        
        if (missingTables.length === 0) {
          console.log('✅ 所有必需的表都已创建')
        } else {
          console.error('❌ 缺少表:', missingTables)
        }
        
        db.close()
        resolve(true)
      }
      
      request.onerror = () => {
        console.error('❌ 新数据库验证失败:', request.error)
        reject(request.error)
      }
    })
    
    // 6. 恢复备份数据
    console.log('\n6️⃣ 恢复备份数据...')
    
    // 等待服务重新初始化
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 恢复API配置
    if (backupData.apiConfigs.length > 0 && typeof strategyStateService !== 'undefined') {
      try {
        const apiConfig = backupData.apiConfigs[0]
        await strategyStateService.saveApiConfig({
          apiKey: apiConfig.apiKey,
          secretKey: apiConfig.secretKey,
          testnet: apiConfig.testnet,
          useProxy: apiConfig.useProxy,
          proxyHost: apiConfig.proxyHost,
          proxyPort: apiConfig.proxyPort
        })
        console.log('✅ API配置已恢复')
      } catch (restoreError) {
        console.warn('⚠️ API配置恢复失败:', restoreError)
      }
    }
    
    console.log('\n🎉 ===== 数据库修复完成 =====')
    console.log('✅ 数据库已重建，所有表结构正确')
    console.log('💡 请刷新页面以使用新的数据库')
    
    return {
      success: true,
      backup: backupData,
      message: '数据库修复成功，请刷新页面'
    }
    
  } catch (error) {
    console.error('❌ 数据库修复失败:', error)
    return {
      success: false,
      error: error.message,
      backup: backupData
    }
  }
}

// 快速诊断数据库问题
async function diagnoseDatabaseIssues() {
  console.log('\n🔍 ===== 数据库问题诊断 =====')
  
  try {
    const dbName = 'QuantStrategyStateDB'
    
    // 检查数据库是否存在
    const databases = await indexedDB.databases()
    const ourDb = databases.find(db => db.name === dbName)
    
    if (!ourDb) {
      console.log('❌ 数据库不存在')
      return { issue: 'database_not_found', recommendation: 'runDatabaseInit' }
    }
    
    console.log(`✅ 数据库存在，版本: ${ourDb.version}`)
    
    // 检查数据库结构
    await new Promise((resolve, reject) => {
      const request = indexedDB.open(dbName)
      
      request.onsuccess = () => {
        const db = request.result
        console.log('📊 当前数据库表:', Array.from(db.objectStoreNames))
        
        const expectedTables = ['strategy_configs', 'system_configs', 'api_configs']
        const missingTables = expectedTables.filter(table => !db.objectStoreNames.contains(table))
        
        if (missingTables.length > 0) {
          console.log('❌ 缺少必需的表:', missingTables)
          console.log('🔧 建议运行数据库修复: fixDatabase()')
          resolve({ issue: 'missing_tables', missingTables, recommendation: 'fixDatabase' })
        } else {
          console.log('✅ 数据库结构完整')
          resolve({ issue: 'none', message: '数据库结构正常' })
        }
        
        db.close()
      }
      
      request.onerror = () => {
        console.error('❌ 无法连接数据库:', request.error)
        reject({ issue: 'connection_error', error: request.error })
      }
    })
    
  } catch (error) {
    console.error('❌ 诊断过程中出现错误:', error)
    return { issue: 'diagnosis_error', error: error.message }
  }
}

// 测试修复后的数据库
async function testFixedDatabase() {
  console.log('\n🧪 ===== 测试修复后的数据库 =====')
  
  try {
    if (typeof strategyStateService === 'undefined') {
      console.error('❌ strategyStateService未加载')
      return false
    }
    
    // 测试API配置保存
    console.log('🔑 测试API配置保存...')
    const testConfig = {
      apiKey: 'test_key_' + Date.now(),
      secretKey: 'test_secret_' + Date.now(),
      testnet: true,
      useProxy: true,
      proxyHost: '127.0.0.1',
      proxyPort: '7890'
    }
    
    const saveResult = await strategyStateService.saveApiConfig(testConfig)
    if (saveResult) {
      console.log('✅ API配置保存测试成功')
      
      // 测试读取
      const savedConfig = await strategyStateService.getApiConfig()
      if (savedConfig && savedConfig.apiKey === testConfig.apiKey) {
        console.log('✅ API配置读取测试成功')
        
        // 清理测试数据
        await strategyStateService.deleteApiConfig()
        console.log('✅ 测试数据清理完成')
        
        return true
      } else {
        console.error('❌ API配置读取测试失败')
        return false
      }
    } else {
      console.error('❌ API配置保存测试失败')
      return false
    }
    
  } catch (error) {
    console.error('❌ 数据库测试失败:', error)
    return false
  }
}

// 暴露函数到全局
window.fixDatabase = fixDatabase
window.diagnoseDatabaseIssues = diagnoseDatabaseIssues
window.testFixedDatabase = testFixedDatabase

console.log(`
🛠️ 数据库修复工具已加载

可用命令:
• diagnoseDatabaseIssues()  - 诊断数据库问题
• fixDatabase()            - 修复数据库（删除重建）
• testFixedDatabase()      - 测试修复后的数据库

建议使用顺序:
1. diagnoseDatabaseIssues() - 先诊断问题
2. fixDatabase()           - 如果有问题则修复
3. testFixedDatabase()     - 验证修复结果
4. 刷新页面使用新数据库

⚠️ 注意: fixDatabase() 会删除现有数据库，会尝试备份但可能丢失数据
`)

// 自动运行诊断
console.log('\n🚀 开始自动诊断...')
setTimeout(() => {
  diagnoseDatabaseIssues()
}, 1000) 