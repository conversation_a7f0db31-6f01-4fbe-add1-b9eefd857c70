import { useState, useEffect, useCallback } from 'react'
import { RiskMonitoring } from './RiskMonitoring'
import { OrderExecution } from './OrderExecution'
import { PerformanceMonitorDisplay } from './PerformanceMonitorDisplay'
import { globalPriceService, PriceDataState } from '@/services/globalPriceService'
// import { priceDataUnifier, UnifiedPriceData, PriceDataQuality } from '@/services/priceDataUnifier'
import { 
  strategyIndicators, 
  StrategyIndicators, 
  getStrategyModeInfo, 
  getMarketStateInfo
} from '@/services/strategyIndicators'
import { 
  accountService, 
  AccountInfo, 
  Position,
  formatNumber,
  getPositionSide,
  getCryptoIcon
} from '@/services/accountService'
import { strategyExecutor, StrategyStatus } from '@/services/strategyExecutor'
import { performanceMonitor } from '@/services/performanceMonitor'

export function RealTimeMonitoring() {
  // 使用全局价格服务的状态
  const [priceData, setPriceData] = useState<PriceDataState | null>(null)
  const [activeTab, setActiveTab] = useState<'overview' | 'risk' | 'orders' | 'performance'>('overview')
  const [performanceExpanded, setPerformanceExpanded] = useState(false)
  const [indicators, setIndicators] = useState<StrategyIndicators | null>(null)
  
  // 账户相关状态
  const [accountInfo, setAccountInfo] = useState<AccountInfo | null>(null)
  const [accountConnected, setAccountConnected] = useState<boolean>(false)
  
  // 策略执行器状态
  const [strategyStatus, setStrategyStatus] = useState<StrategyStatus | null>(null)

  // 获取当前状态信息
  const tradingStatus = localStorage.getItem('trading_status') || '未设置'
  const selectedSymbol = localStorage.getItem('selected_symbol') || '未设置'
  const isMonitoring = tradingStatus === 'active' && selectedSymbol !== '未设置'
  const isConnected = priceData?.isConnected || false

  // 🔧 策略状态更新函数 - 提取到组件级别
  const updateStrategyStatus = useCallback(() => {
    try {
      // 检查策略执行器是否运行
      const strategyRunning = strategyExecutor.isStrategyRunning()
      console.log('🔍 策略状态检查:', {
        isMonitoring,
        strategyRunning,
        tradingStatus,
        selectedSymbol
      })
      
      if (strategyRunning) {
        const status = strategyExecutor.getStrategyStatus()
        setStrategyStatus(status)
        console.log('✅ 策略状态已更新:', status)
      } else {
        setStrategyStatus(null)
        console.log('⚪ 策略未运行，状态设为null')
      }
    } catch (error) {
      console.error('❌ 策略状态检查失败:', error)
      setStrategyStatus(null)
    }
  }, [isMonitoring, tradingStatus, selectedSymbol])

  // 页面初始化 - 只订阅数据，不管理连接
  useEffect(() => {
    console.log('🔧 实时监控页面初始化 - 订阅全局价格数据')
    
    // 检查localStorage中的交易状态，如果是active但策略执行器未运行，尝试启动
    const initializeStrategyIfNeeded = async () => {
      const storedStatus = localStorage.getItem('trading_status')
      const storedSymbol = localStorage.getItem('selected_symbol')
      
      console.log('🔍 初始化检查:', {
        storedStatus,
        storedSymbol,
        isStrategyRunning: strategyExecutor.isStrategyRunning()
      })
      
      if (storedStatus === 'active' && storedSymbol && !strategyExecutor.isStrategyRunning()) {
        console.log('🚀 检测到交易状态为active但策略执行器未运行，尝试启动...')
        try {
          const startResult = await strategyExecutor.startStrategy(storedSymbol)
          if (startResult) {
            console.log('✅ 策略执行器启动成功')
            // 立即更新策略状态
            setTimeout(() => {
              const status = strategyExecutor.getStrategyStatus()
              setStrategyStatus(status)
              console.log('✅ 策略状态已更新:', status)
            }, 1000)
          } else {
            console.error('❌ 策略执行器启动失败')
          }
        } catch (error) {
          console.error('❌ 自动启动策略执行器失败:', error)
        }
      }
    }
    
    // 延迟1秒执行，确保页面完全加载
    setTimeout(initializeStrategyIfNeeded, 1000)
    
    // 监听 localStorage 变化，响应策略状态变更
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'trading_status') {
        console.log('📡 监听到 trading_status 变化:', e.newValue)
        // 🔧 安全修复：移除页面刷新，改为状态重新检查
        setTimeout(() => {
          console.log('🔄 重新检查交易状态和策略执行器状态')
          
          const newTradingStatus = localStorage.getItem('trading_status')
          const newSelectedSymbol = localStorage.getItem('selected_symbol')
          
          // 触发策略状态重新检查
          updateStrategyStatus()
          
          // 如果状态变为active，但策略未运行，尝试启动
          if (newTradingStatus === 'active' && newSelectedSymbol && !strategyExecutor.isStrategyRunning()) {
            console.log('🚀 检测到需要启动策略执行器')
            strategyExecutor.startStrategy(newSelectedSymbol)
              .then(result => {
                if (result) {
                  console.log('✅ 策略执行器启动成功')
                } else {
                  console.error('❌ 策略执行器启动失败')
                }
              })
              .catch(error => {
                console.error('❌ 策略执行器启动异常:', error)
              })
          }
        }, 500)
      }
    }
    
    window.addEventListener('storage', handleStorageChange)
    
    // 订阅全局价格数据
    const unsubscribe = globalPriceService.subscribe((state: PriceDataState) => {
      console.log('🔍 [FRONTEND] 收到全局价格服务状态更新:', {
        currentPrice: state.currentPrice,
        bid: state.bid,
        ask: state.ask,
        isConnected: state.isConnected,
        lastUpdate: new Date(state.lastUpdate).toLocaleTimeString(),
        change24h: state.change24h
      })

      setPriceData(state)

      // 初始化或更新策略指标 (基于K线数据)
      if (state.currentPrice > 0 && state.isConnected && selectedSymbol && selectedSymbol !== '未设置') {
        // 检查是否需要初始化K线数据
        const dataStatus = strategyIndicators.getDataStatus()
        if (!dataStatus.initialized || dataStatus.symbol !== selectedSymbol) {
          console.log('🚀 初始化K线数据系统:', selectedSymbol)
          strategyIndicators.initializeKlineData(selectedSymbol)
            .then((newIndicators) => {
              setIndicators(newIndicators)
              console.log('✅ K线指标初始化完成:', {
                symbol: selectedSymbol,
                klineCount: newIndicators.klineCount,
                dataReady: newIndicators.dataReady,
                marketState: newIndicators.marketState,
                strategyMode: newIndicators.currentMode
              })
            })
            .catch((error) => {
              console.error('❌ K线指标初始化失败:', error)
            })
        } else {
          // 获取当前指标 (基于缓存的K线数据)
          const currentIndicators = strategyIndicators.getCurrentIndicators()
          setIndicators(currentIndicators)
        }
      }
    })

    // 初始化账户服务
    const initAccountService = async () => {
      if (isMonitoring) {
        console.log('💼 初始化账户服务')
        
        // 检查系统配置状态
        const configStatus = accountService.getSystemConfigStatus()
        console.log('📊 系统配置状态:', configStatus)
        
        if (!configStatus.isValid) {
          console.warn('⚠️ 系统配置不完整:', configStatus.message)
          setAccountConnected(false)
          setAccountInfo(null)
          return
        }
        
        try {
          // 重新加载系统配置中的API凭证
          const credentialsLoaded = accountService.loadCredentialsFromSystemConfig()
          if (!credentialsLoaded) {
            console.error('❌ 无法从系统配置加载API凭证')
            setAccountConnected(false)
            setAccountInfo(null)
            return
          }

          console.log('🔄 开始获取账户信息...')
          // 获取初始账户信息
          const info = await accountService.getAccountInfo()
          console.log('📈 账户信息获取结果:', info ? '成功' : '失败')
          
          setAccountInfo(info)
          setAccountConnected(accountService.getConnectionStatus())
          
          if (info) {
            // 启动自动更新 (每10秒更新一次)
            accountService.startAutoUpdate(10000)
            console.log('✅ 账户服务初始化成功')
            console.log('💰 账户权益:', info.totalMarginBalance, 'USDT')
            console.log('📊 活跃持仓:', info.positions.filter(p => Math.abs(parseFloat(p.positionAmt)) > 0).length, '个')
          } else {
            console.error('❌ 无法获取账户信息，请检查系统配置中的API凭证')
            console.error('🔍 请检查以下几点:')
            console.error('  1. API Key和Secret Key是否正确')
            console.error('  2. API Key是否有期货交易权限')
            console.error('  3. 如果在国内，是否需要启用代理')
            console.error('  4. 网络连接是否正常')
          }
        } catch (error) {
          console.error('❌ 账户服务初始化失败:', error)
          setAccountConnected(false)
          setAccountInfo(null)
        }
      } else {
        // 停止账户服务
        accountService.stopAutoUpdate()
        setAccountInfo(null)
        setAccountConnected(false)
      }
    }

    initAccountService()



    // 定期更新账户信息显示 (从缓存获取，避免频繁API调用)
    const updateAccountInfo = () => {
      if (isMonitoring) {
        const cachedInfo = accountService.getCachedAccountInfo()
        if (cachedInfo) {
          setAccountInfo(cachedInfo)
          setAccountConnected(accountService.getConnectionStatus())
        }
      }
    }

    const accountUpdateInterval = setInterval(updateAccountInfo, 3000)

      // 策略状态监控 - 使用提取出来的updateStrategyStatus函数
    updateStrategyStatus()
    const strategyUpdateInterval = setInterval(updateStrategyStatus, 2000)

    return () => {
      console.log('页面卸载 - 取消订阅价格数据')
      unsubscribe()
      clearInterval(accountUpdateInterval)
      clearInterval(strategyUpdateInterval)
      window.removeEventListener('storage', handleStorageChange)
      // 不在这里停止账户服务，因为可能其他页面还在使用
    }
  }, [priceData, updateStrategyStatus])

  // 获取系统配置状态
  const systemConfigStatus = accountService.getSystemConfigStatus()

  // 手动测试功能
  const handleManualTest = () => {
    console.log('🧪 手动测试功能')
    
    // 强制重连
    globalPriceService.reconnect()
    
    // 显示当前状态
    console.log('当前状态:', {
      localStorage: {
        trading_status: localStorage.getItem('trading_status'),
        selected_symbol: localStorage.getItem('selected_symbol')
      },
      globalService: {
        isConnected: globalPriceService.isConnected(),
        currentSymbol: globalPriceService.getCurrentSymbol(),
        currentPrice: globalPriceService.getCurrentPrice()
      }
    })
  }

  // 手动启动交易测试
  const handleStartTrading = () => {
    console.log('🚀 手动启动交易测试')
    localStorage.setItem('trading_status', 'active')
    localStorage.setItem('selected_symbol', 'BTCUSDT')
    
    // 🔧 安全修复：移除页面刷新，改为直接启动策略
    setTimeout(async () => {
      console.log('🔄 直接启动策略执行器')
      try {
        const result = await strategyExecutor.startStrategy('BTCUSDT')
        if (result) {
          console.log('✅ 测试交易启动成功')
          // 更新本地状态
          updateStrategyStatus()
        } else {
          console.error('❌ 测试交易启动失败')
        }
      } catch (error) {
        console.error('❌ 测试交易启动异常:', error)
      }
    }, 1000)
  }

  // 刷新账户数据
  const handleRefreshAccount = async () => {
    console.log('🔄 手动刷新账户数据')
    try {
      // 重新检查系统配置
      const configStatus = accountService.getSystemConfigStatus()
      console.log('📊 系统配置检查:', configStatus)
      
      if (!configStatus.isValid) {
        console.error('❌ 系统配置无效:', configStatus.message)
        alert('系统配置无效: ' + configStatus.message)
        return
      }

      // 重新加载凭证
      const credentialsLoaded = accountService.loadCredentialsFromSystemConfig()
      if (!credentialsLoaded) {
        console.error('❌ 凭证加载失败')
        alert('无法加载API凭证，请检查系统设置')
        return
      }

      console.log('🔄 尝试重新连接账户...')
      const info = await accountService.getAccountInfo()
      setAccountInfo(info)
      setAccountConnected(accountService.getConnectionStatus())
      
      if (info) {
        console.log('✅ 账户数据刷新成功')
        alert('✅ 账户数据刷新成功!')
      } else {
        console.error('❌ 账户数据刷新失败')
        alert('❌ 账户数据刷新失败，请检查控制台错误信息')
      }
    } catch (error) {
      console.error('❌ 账户数据刷新失败:', error)
      alert('❌ 账户数据刷新失败: ' + (error as Error).message)
    }
  }

  // 检查并显示详细的连接状态
  const getDetailedConnectionStatus = () => {
    const configStatus = accountService.getSystemConfigStatus()
    const hasValidCredentials = accountService.hasValidCredentials()
    
    return {
      configStatus,
      hasValidCredentials,
      isMonitoring,
      accountConnected,
      hasAccountInfo: !!accountInfo
    }
  }

  // 跳转到系统设置页面
  const handleGoToSettings = () => {
    window.location.href = '/settings'
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 p-6">




      {/* 页面标题 */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white">🚀 实时监控中心</h1>
            <p className="text-slate-400 mt-1">币安U本位合约实时数据监控系统</p>
          </div>
          
          {/* 连接状态指示器和API管理 */}
          <div className="flex items-center gap-4">
            <div className={`flex items-center gap-2 px-3 py-2 rounded-lg ${
              isConnected && isMonitoring ? 'bg-green-500/20 text-green-400' : 
              isMonitoring ? 'bg-blue-500/20 text-blue-400' :
              'bg-gray-500/20 text-gray-400'
            }`}>
              <div className={`w-3 h-3 rounded-full ${
                isConnected && isMonitoring ? 'bg-green-400 animate-pulse' : 
                isMonitoring ? 'bg-blue-400 animate-pulse' :
                'bg-gray-400'
              }`}></div>
              <span className="font-medium">
                {isConnected && isMonitoring ? `✅ 运行中 ${selectedSymbol}` : 
                 isMonitoring ? `🔄 连接中 ${selectedSymbol}` :
                 '⏸️ 未启动监控'}
              </span>
            </div>
            
            {/* 系统配置状态提示 */}
            <div className="flex items-center gap-2">
              {!systemConfigStatus.isValid && (
                <button
                  onClick={handleGoToSettings}
                  className="px-3 py-1 bg-orange-600/20 hover:bg-orange-600/30 border border-orange-600/30 rounded text-orange-400 text-sm transition-colors"
                >
                  ⚙️ 配置API
                </button>
              )}
              {systemConfigStatus.isValid && (
                <span className="px-3 py-1 bg-green-600/20 border border-green-600/30 rounded text-green-400 text-sm">
                  ✅ API已配置
                </span>
              )}
            </div>
            
            <div className="text-xs text-slate-400">
              最后更新: {priceData?.lastUpdate ? new Date(priceData.lastUpdate).toLocaleTimeString() : '--:--:--'}
            </div>
          </div>
        </div>
      </div>

      {/* 导航标签页 */}
      <div className="flex gap-1 p-1 bg-slate-800/30 rounded-lg mb-6">
        <button
          onClick={() => setActiveTab('overview')}
          className={`flex-1 px-4 py-2 rounded-md font-medium transition-all ${
            activeTab === 'overview' 
              ? 'bg-blue-500 text-white shadow-lg' 
              : 'text-slate-400 hover:text-white hover:bg-slate-700/50'
          }`}
        >
          📊 总览
        </button>
        <button
          onClick={() => setActiveTab('risk')}
          className={`flex-1 px-4 py-2 rounded-md font-medium transition-all ${
            activeTab === 'risk' 
              ? 'bg-blue-500 text-white shadow-lg' 
              : 'text-slate-400 hover:text-white hover:bg-slate-700/50'
          }`}
        >
          🛡️ 风险监控
        </button>
        <button
          onClick={() => setActiveTab('orders')}
          className={`flex-1 px-4 py-2 rounded-md font-medium transition-all ${
            activeTab === 'orders' 
              ? 'bg-blue-500 text-white shadow-lg' 
              : 'text-slate-400 hover:text-white hover:bg-slate-700/50'
          }`}
        >
          ⚡ 订单执行
        </button>
        <button
          onClick={() => setActiveTab('performance')}
          className={`flex-1 px-4 py-2 rounded-md font-medium transition-all ${
            activeTab === 'performance' 
              ? 'bg-blue-500 text-white shadow-lg' 
              : 'text-slate-400 hover:text-white hover:bg-slate-700/50'
          }`}
        >
          📊 性能监控
        </button>

      </div>

      {/* 根据选择的标签页显示内容 */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* 状态检查提醒 */}
          {!isMonitoring && (
            <div className="bg-yellow-500/10 backdrop-blur-sm rounded-xl border border-yellow-500/30 p-6">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 rounded-full bg-yellow-500/20 flex items-center justify-center">
                  <span className="text-2xl">⚠️</span>
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-yellow-400">监控未启动</h3>
                  <p className="text-yellow-300 mt-1">
                    检测到交易状态为：<strong>{tradingStatus}</strong>，币种：<strong>{selectedSymbol}</strong>
                  </p>
                  <p className="text-yellow-300/80 text-sm mt-1">
                    请前往 "策略配置" 页面选择交易币种并点击 "开始交易"，或使用下方测试按钮。
                  </p>
                </div>
                <button
                  onClick={handleStartTrading}
                  className="px-4 py-2 bg-yellow-600/30 hover:bg-yellow-600/40 border border-yellow-600/50 rounded-lg text-yellow-300 transition-colors"
                >
                  🚀 启动测试
                </button>
              </div>
            </div>
          )}

          {/* 成功运行提醒 */}
          {isConnected && isMonitoring && (
            <div className="bg-green-500/10 backdrop-blur-sm rounded-xl border border-green-500/30 p-6">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 rounded-full bg-green-500/20 flex items-center justify-center">
                  <span className="text-2xl">✅</span>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-green-400">系统运行正常</h3>
                  <p className="text-green-300 mt-1">
                    正在监控 <strong>{selectedSymbol}</strong> 实时价格数据，当前价格：<strong>${priceData?.currentPrice.toFixed(2)}</strong>
                  </p>
                                     <p className="text-green-300/80 text-sm mt-1">
                     WebSocket连接稳定，策略数据完整 • 精简WebSocket订阅
                   </p>
                </div>
              </div>
            </div>
          )}

          {/* 全局价格服务监控中心 - 融合详细信息 */}
          <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700/50 p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-4">
                <div className={`flex items-center gap-2 ${
                  isConnected && isMonitoring ? 'text-green-400' : 
                  isMonitoring ? 'text-blue-400' : 'text-gray-400'
                }`}>
                  <div className={`w-3 h-3 rounded-full ${
                    isConnected && isMonitoring ? 'bg-green-400 animate-pulse' : 
                    isMonitoring ? 'bg-blue-400 animate-pulse' :
                    'bg-gray-400'
                  }`}></div>
                  <span className="font-medium text-lg">
                    🔧 全局价格服务: {isConnected && isMonitoring ? '✅ 运行中' : 
                                   isMonitoring ? '🔄 连接中' : '⏸️ 已停止'}
                  </span>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <button
                  onClick={handleManualTest}
                  className="px-3 py-1 bg-green-600/20 hover:bg-green-600/30 border border-green-600/30 rounded text-green-400 text-sm transition-colors"
                >
                  🧪 测试连接
                </button>
                <button
                  onClick={() => globalPriceService.reconnect()}
                  className="px-3 py-1 bg-blue-600/20 hover:bg-blue-600/30 border border-blue-600/30 rounded text-blue-400 text-sm transition-colors"
                  disabled={!isMonitoring}
                >
                  🔄 重连
                </button>

                <div className="text-sm text-slate-400">
                  更新: {priceData?.lastUpdate ? new Date(priceData.lastUpdate).toLocaleTimeString() : '无数据'}
                </div>
              </div>
            </div>

            {/* 实时价格行情显示 - 融合实时行情 */}
            {isMonitoring && priceData && priceData.currentPrice > 0 ? (
              <div className="bg-gradient-to-r from-blue-500/10 to-green-500/10 rounded-lg border border-blue-500/30 p-4 mb-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <span className="text-2xl">⚡</span>
                    <div>
                      <div className="text-lg font-bold text-white">{selectedSymbol}</div>
                      <div className="text-xs text-slate-400">期货合约实时行情</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className={`text-sm font-semibold ${
                      priceData.change24h >= 0 ? 'text-green-400' : 'text-red-400'
                    }`}>
                      {priceData.change24h >= 0 ? '+' : ''}{priceData.change24h.toFixed(2)}%
                    </div>
                    <div className="text-xs text-slate-400">24h变化</div>
                  </div>
                </div>
                
                <div className="flex items-center justify-between mb-3">
                  <div className="text-3xl font-bold text-white">
                    ${priceData.currentPrice.toFixed(2)}
                  </div>
                  <div className="flex items-center gap-4">
                    <div className="text-center">
                      <div className="text-xs text-slate-400">买入价</div>
                      <div className="text-green-400 font-semibold">${priceData.bid.toFixed(2)}</div>
                    </div>
                    <div className="text-center">
                      <div className="text-xs text-slate-400">卖出价</div>
                      <div className="text-red-400 font-semibold">${priceData.ask.toFixed(2)}</div>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-3 gap-4 text-xs text-slate-400">
                  <div>
                    <span>24h成交量:</span>
                    <span className="text-white ml-1">{priceData.volume24h.toFixed(0)}</span>
                  </div>
                  <div>
                    <span>24h最高:</span>
                    <span className="text-green-400 ml-1">${priceData.high24h.toFixed(2)}</span>
                  </div>
                  <div>
                    <span>24h最低:</span>
                    <span className="text-red-400 ml-1">${priceData.low24h.toFixed(2)}</span>
                  </div>
                </div>
                
                <div className="mt-2 text-xs text-green-400">
                  ✅ 期货K线数据 (精简WebSocket) • K线数量: {indicators?.klineCount || 0} • 买卖价差: ${globalPriceService.getSpread()?.toFixed(4) || '0.0000'}
                </div>
              </div>
            ) : (
              <div className="bg-slate-700/30 rounded-lg border border-slate-600/30 p-6 mb-4 text-center">
                <div className="text-3xl mb-3">📶</div>
                <div className="text-slate-400">
                  {isMonitoring ? '等待价格数据...' : '请启动交易以查看实时行情'}
                </div>
                <div className="text-sm text-slate-500 mt-1">
                  {isMonitoring ? '全局价格服务连接中' : '监控未启动'}
                </div>
                {!isMonitoring && (
                  <button
                    onClick={handleStartTrading}
                    className="mt-3 px-4 py-2 bg-blue-600/30 hover:bg-blue-600/40 border border-blue-600/50 rounded text-blue-400 transition-colors"
                  >
                    🚀 启动监控
                  </button>
                )}
              </div>
            )}

            {/* 服务状态网格 */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
              {/* 服务状态 */}
              <div className="bg-slate-700/50 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-slate-400">服务状态</span>
                  <span className={`font-bold ${
                    globalPriceService.isConnected() ? 'text-green-400' : 'text-red-400'
                  }`}>
                    {globalPriceService.isConnected() ? '✅ 运行中' : '❌ 已停止'}
                  </span>
                </div>
                <div className="text-xs text-slate-400">
                  {isMonitoring ? '与交易状态同步' : '等待交易启动'}
                </div>
              </div>

              {/* 监控币种 */}
              <div className="bg-slate-700/50 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-slate-400">监控币种</span>
                  <span className="text-white font-bold">
                    {globalPriceService.getCurrentSymbol() || selectedSymbol || 'N/A'}
                  </span>
                </div>
                <div className="text-xs text-slate-400">
                  {isMonitoring ? '活跃监控' : '请选择币种'}
                </div>
              </div>

              {/* 数据源 */}
              <div className="bg-slate-700/50 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-slate-400">数据源</span>
                  <span className="text-white font-bold">
                    币安期货
                  </span>
                </div>
                <div className="text-xs text-slate-400">
                  {isConnected ? 'WebSocket实时' : '连接中'}
                </div>
              </div>

              {/* 数据更新 */}
              <div className="bg-slate-700/50 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-slate-400">数据更新</span>
                  <span className="text-white font-bold">
                    {isConnected ? '✅ 实时' : '❌ 停止'}
                  </span>
                </div>
                <div className="text-xs text-slate-400">
                  {isConnected ? 'WebSocket推送' : '无数据流'}
                </div>
              </div>
            </div>

            {/* 系统状态信息 */}
            <div className="pt-3 border-t border-slate-600/30">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs text-slate-400 mb-3">
                <div>
                  <span className="text-slate-500">交易状态:</span>
                  <span className={`ml-1 font-medium ${tradingStatus === 'active' ? 'text-green-400' : 'text-yellow-400'}`}>
                    {tradingStatus}
                  </span>
                </div>
                <div>
                  <span className="text-slate-500">全局服务:</span>
                  <span className={`ml-1 font-medium ${globalPriceService.isConnected() ? 'text-green-400' : 'text-yellow-400'}`}>
                    {globalPriceService.isConnected() ? '运行中' : '未连接'}
                  </span>
                </div>
                <div>
                  <span className="text-slate-500">24h变化:</span>
                  <span className={`ml-1 font-medium ${(priceData?.change24h || 0) >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                    {priceData?.change24h ? `${priceData.change24h >= 0 ? '+' : ''}${priceData.change24h.toFixed(2)}%` : '--'}
                  </span>
                </div>
                <div>
                  <span className="text-slate-500">成交量:</span>
                  <span className="text-white ml-1 font-medium">
                    {priceData?.volume24h ? `${(priceData.volume24h / 1000).toFixed(0)}K` : '--'}
                  </span>
                </div>
              </div>
              
              <div className="bg-blue-500/10 rounded-lg border border-blue-500/30 p-3">
                <div className="text-sm text-blue-400">
                  💡 <strong>架构升级</strong>: WebSocket连接现由全局价格服务统一管理，与交易状态同步，为所有模块提供实时数据 • 点击 "调试面板" 查看详细信息
                </div>
              </div>
            </div>
          </div>

          {/* 🎯 策略模式显示 - 移动到全局价格服务下方 */}
          {indicators ? (
            <div className="mb-6">
              <div className={`rounded-2xl border-2 p-6 backdrop-blur-sm ${
                getStrategyModeInfo(indicators.currentMode).bgColor} ${
                getStrategyModeInfo(indicators.currentMode).borderColor
              }`}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-6">
                    <div className="flex items-center gap-3">
                      <div className="text-4xl">
                        {getStrategyModeInfo(indicators.currentMode).icon}
                      </div>
                      <div>
                        <div className={`text-2xl font-bold ${getStrategyModeInfo(indicators.currentMode).color}`}>
                          当前策略模式: {indicators.currentMode}
                        </div>
                        <div className="text-slate-300 mt-1">
                          {getStrategyModeInfo(indicators.currentMode).description} • 
                          历史频率: {getStrategyModeInfo(indicators.currentMode).frequency} • 
                          置信度: {indicators.modeConfidence.toFixed(1)}%
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-4">
                      <div className={`flex items-center gap-2 ${getMarketStateInfo(indicators.marketState).color}`}>
                        <span className="text-xl">{getMarketStateInfo(indicators.marketState).icon}</span>
                        <div>
                          <div className="font-semibold">{indicators.marketState}</div>
                          <div className="text-xs opacity-80">{getMarketStateInfo(indicators.marketState).description}</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="text-right">
                    <div className="text-sm text-slate-400 mb-2">策略执行状态</div>
                    <div className={`text-lg font-bold ${
                      isConnected && isMonitoring ? 'text-green-400' : 
                      isMonitoring ? 'text-yellow-400' : 'text-gray-400'
                    }`}>
                      {isConnected && isMonitoring ? '🟢 执行中' : 
                       isMonitoring ? '🟡 待连接' : '🔴 已停止'}
                    </div>
                    <div className="text-xs text-slate-400 mt-1">
                      {isConnected && isMonitoring ? '策略正在自动执行' : 
                       isMonitoring ? '等待市场数据' : '请启动交易'}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : isMonitoring ? (
            <div className="mb-6 bg-blue-500/10 backdrop-blur-sm rounded-2xl border-2 border-blue-500/30 p-6">
              <div className="flex items-center gap-4">
                <div className="text-3xl">📊</div>
                <div>
                  <div className="text-xl font-bold text-blue-400">正在初始化策略分析系统...</div>
                  <div className="text-slate-300 mt-1">
                    收集价格数据中，需要足够数据点才能计算技术指标和识别策略模式
                  </div>
                </div>
              </div>
            </div>
          ) : null}

          {/* 重新设计的网格布局 - 2组件布局 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        


            {/* 1. 核心技术指标 */}
            <div className="lg:col-span-1">
              <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700/50 p-5 h-full flex flex-col">
                                  <h2 className="text-xl font-semibold text-white mb-4 flex items-center gap-2">
                  📊 核心技术指标
                  {indicators && (
                    <span className="text-xs bg-green-500/20 text-green-400 px-2 py-1 rounded">
                      K线数据: {indicators.klineCount}/200 • {indicators.dataReady ? '✅ 就绪' : '⏳ 初始化'}
                    </span>
                  )}
                </h2>
                
                <div className="space-y-4 flex-1">
                  {indicators ? (
                    <>
                      {/* 移动平均线 - 优化布局 */}
                      <div className="bg-slate-700/50 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <span className="text-slate-400">移动平均线 (完整斐波那契周期)</span>
                          <span className="text-xs text-slate-400">
                            当前价格: ${priceData?.currentPrice.toFixed(2)}
                          </span>
                        </div>
                        <div className="grid grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-slate-400">MA3:</span>
                            <span className={`font-medium ${
                              indicators.ma_3 > 0 ? 
                                ((priceData?.currentPrice || 0) > indicators.ma_3 ? 'text-green-400' : 'text-red-400')
                                : 'text-gray-500'
                            }`}>
                              {indicators.ma_3 > 0 ? `$${indicators.ma_3.toFixed(2)}` : '计算中...'}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-slate-400">MA5:</span>
                            <span className={`font-medium ${
                              indicators.ma_5 > 0 ? 
                                ((priceData?.currentPrice || 0) > indicators.ma_5 ? 'text-green-400' : 'text-red-400')
                                : 'text-gray-500'
                            }`}>
                              {indicators.ma_5 > 0 ? `$${indicators.ma_5.toFixed(2)}` : '需要5个数据点'}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-slate-400">MA8:</span>
                            <span className={`font-medium ${
                              indicators.ma_8 > 0 ? 
                                ((priceData?.currentPrice || 0) > indicators.ma_8 ? 'text-green-400' : 'text-red-400')
                                : 'text-gray-500'
                            }`}>
                              {indicators.ma_8 > 0 ? `$${indicators.ma_8.toFixed(2)}` : '需要8个数据点'}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-slate-400">MA13:</span>
                            <span className={`font-medium ${
                              indicators.ma_13 > 0 ? 
                                ((priceData?.currentPrice || 0) > indicators.ma_13 ? 'text-green-400' : 'text-red-400')
                                : 'text-gray-500'
                            }`}>
                              {indicators.ma_13 > 0 ? `$${indicators.ma_13.toFixed(2)}` : '需要13个数据点'}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-slate-400">MA21:</span>
                            <span className={`font-medium ${
                              indicators.ma_21 > 0 ? 
                                ((priceData?.currentPrice || 0) > indicators.ma_21 ? 'text-green-400' : 'text-red-400')
                                : 'text-gray-500'
                            }`}>
                              {indicators.ma_21 > 0 ? `$${indicators.ma_21.toFixed(2)}` : '需要21个数据点'}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-slate-400">MA34:</span>
                            <span className={`font-medium ${
                              indicators.ma_34 > 0 ? 
                                ((priceData?.currentPrice || 0) > indicators.ma_34 ? 'text-green-400' : 'text-red-400')
                                : 'text-gray-500'
                            }`}>
                              {indicators.ma_34 > 0 ? `$${indicators.ma_34.toFixed(2)}` : '需要34个数据点'}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-slate-400">MA55:</span>
                            <span className={`font-medium ${
                              indicators.ma_55 > 0 ? 
                                ((priceData?.currentPrice || 0) > indicators.ma_55 ? 'text-green-400' : 'text-red-400')
                                : 'text-gray-500'
                            }`}>
                              {indicators.ma_55 > 0 ? `$${indicators.ma_55.toFixed(2)}` : '需要55个数据点'}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-slate-400">MA89:</span>
                            <span className={`font-medium ${
                              indicators.ma_89 > 0 ? 
                                ((priceData?.currentPrice || 0) > indicators.ma_89 ? 'text-green-400' : 'text-red-400')
                                : 'text-gray-500'
                            }`}>
                              {indicators.ma_89 > 0 ? `$${indicators.ma_89.toFixed(2)}` : '需要89个数据点'}
                            </span>
                          </div>
                        </div>
                        <div className="mt-3 pt-2 border-t border-slate-600/30">
                          <div className="grid grid-cols-4 gap-3 text-sm">
                            <div className="flex justify-between">
                              <span className="text-slate-400">EMA8:</span>
                              <span className={`font-medium ${indicators.ema_8 > 0 ? 'text-blue-400' : 'text-gray-500'}`}>
                                {indicators.ema_8 > 0 ? `$${indicators.ema_8.toFixed(2)}` : '计算中...'}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-slate-400">EMA21:</span>
                              <span className={`font-medium ${indicators.ema_21 > 0 ? 'text-blue-400' : 'text-gray-500'}`}>
                                {indicators.ema_21 > 0 ? `$${indicators.ema_21.toFixed(2)}` : '计算中...'}
                              </span>
                            </div>
                            <div></div>
                            <div></div>
                          </div>
                          <div className="mt-2 text-xs text-slate-400">
                            💡 基于已完成历史K线计算，避免指标跳动 • MA89需要89根K线 • 当前: {indicators.klineCount}根
                          </div>
                        </div>
                      </div>

                      {/* RSI指标 - 水平布局 */}
                      <div className="bg-slate-700/50 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <span className="text-slate-400">RSI 多周期</span>
                          <span className="text-xs text-slate-400">超买&gt;70 超卖&lt;30</span>
                        </div>
                        <div className="grid grid-cols-2 lg:grid-cols-4 gap-2 text-sm">
                          <div className="flex items-center gap-1">
                            <span className="text-slate-400">RSI(5):</span>
                            <span className={`font-medium ${
                              indicators.rsi_5 > 70 ? 'text-red-400' : 
                              indicators.rsi_5 < 30 ? 'text-green-400' : 'text-yellow-400'
                            }`}>
                              {indicators.rsi_5.toFixed(1)}
                            </span>
                          </div>
                          <div className="flex items-center gap-1">
                            <span className="text-slate-400">RSI(14):</span>
                            <span className={`font-medium ${
                              indicators.rsi_14 > 70 ? 'text-red-400' : 
                              indicators.rsi_14 < 30 ? 'text-green-400' : 'text-yellow-400'
                            }`}>
                              {indicators.rsi_14.toFixed(1)}
                            </span>
                          </div>
                          <div className="flex items-center gap-1">
                            <span className="text-slate-400">RSI(7):</span>
                            <span className={`font-medium ${
                              indicators.rsi_7 > 70 ? 'text-red-400' : 
                              indicators.rsi_7 < 30 ? 'text-green-400' : 'text-yellow-400'
                            }`}>
                              {indicators.rsi_7.toFixed(1)}
                            </span>
                          </div>
                          <div className="flex items-center gap-1">
                            <span className="text-slate-400">RSI(21):</span>
                            <span className={`font-medium ${
                              indicators.rsi_21 > 70 ? 'text-red-400' : 
                              indicators.rsi_21 < 30 ? 'text-green-400' : 'text-yellow-400'
                            }`}>
                              {indicators.rsi_21.toFixed(1)}
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* 动量指标 - 网格布局 */}
                      <div className="bg-slate-700/50 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <span className="text-slate-400">超细分动量指标 (完整时间框架)</span>
                          <span className="text-xs text-slate-400">15分钟到4小时</span>
                        </div>
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-slate-400">15分钟 (momentum_1):</span>
                            <span className={`font-medium ${
                              indicators.momentum_1 > 0 ? 'text-green-400' : 'text-red-400'
                            }`}>
                              {indicators.momentum_1 > 0 ? '+' : ''}{(indicators.momentum_1 * 100).toFixed(3)}%
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-slate-400">30分钟 (momentum_2):</span>
                            <span className={`font-medium ${
                              indicators.momentum_2 > 0 ? 'text-green-400' : 'text-red-400'
                            }`}>
                              {indicators.momentum_2 > 0 ? '+' : ''}{(indicators.momentum_2 * 100).toFixed(3)}%
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-slate-400">1小时 (momentum_4):</span>
                            <span className={`font-medium ${
                              indicators.momentum_4 > 0 ? 'text-green-400' : 'text-red-400'
                            }`}>
                              {indicators.momentum_4 > 0 ? '+' : ''}{(indicators.momentum_4 * 100).toFixed(3)}%
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-slate-400">2小时 (momentum_8):</span>
                            <span className={`font-medium ${
                              indicators.momentum_8 > 0 ? 'text-green-400' : 'text-red-400'
                            }`}>
                              {indicators.momentum_8 > 0 ? '+' : ''}{(indicators.momentum_8 * 100).toFixed(3)}%
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-slate-400">4小时 (momentum_16):</span>
                            <span className={`font-medium ${
                              indicators.momentum_16 > 0 ? 'text-green-400' : 'text-red-400'
                            }`}>
                              {indicators.momentum_16 > 0 ? '+' : ''}{(indicators.momentum_16 * 100).toFixed(3)}%
                            </span>
                          </div>
                        </div>
                      </div>
                    </>
                  ) : (
                    <div className="text-center text-slate-400 py-8 flex-1 flex flex-col justify-center">
                      <div className="mb-3">📊</div>
                      <p>{isMonitoring ? '计算技术指标中...' : '请开启监控以查看技术指标'}</p>
                      <p className="text-sm text-slate-500 mt-1">
                        {isMonitoring ? '需要足够价格数据' : '监控未启动'}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* 2. 策略信号强度 */}
            <div className="lg:col-span-1">
              <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700/50 p-5 h-full flex flex-col">
                <h2 className="text-xl font-semibold text-white mb-4 flex items-center gap-2">
                  🎯 策略信号强度
                  {indicators && (
                    <span className="text-xs bg-blue-500/20 text-blue-400 px-2 py-1 rounded">
                      实时评估
                    </span>
                  )}
                </h2>
                
                {indicators ? (
                  <div className="space-y-3 flex-1 flex flex-col justify-between">
                    {/* 四大策略信号 - 紧凑布局 */}
                    <div className="space-y-4 flex-1">
                      {/* 网格策略信号 */}
                      <div className="bg-slate-700/50 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-slate-400 text-sm">🟦 网格策略</span>
                          <span className={`font-bold text-lg ${
                            indicators.gridSignal > 30 ? 'text-green-400' : 
                            indicators.gridSignal > 15 ? 'text-yellow-400' : 'text-red-400'
                          }`}>
                            {indicators.gridSignal.toFixed(0)}%
                          </span>
                        </div>
                        <div className="w-full bg-slate-800 rounded-full h-1.5">
                          <div 
                            className={`h-1.5 rounded-full ${
                              indicators.gridSignal > 30 ? 'bg-green-400' : 
                              indicators.gridSignal > 15 ? 'bg-yellow-400' : 'bg-red-400'
                            }`}
                            style={{ width: `${Math.min(100, indicators.gridSignal)}%` }}
                          ></div>
                        </div>
                        <div className="text-xs text-slate-500 mt-1">稳定基础 (9.5%)</div>
                      </div>

                      {/* 趋势策略信号 */}
                      <div className="bg-slate-700/50 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-slate-400 text-sm">📈 趋势策略</span>
                          <span className={`font-bold text-lg ${
                            indicators.trendSignal > 50 ? 'text-green-400' : 
                            indicators.trendSignal > 25 ? 'text-yellow-400' : 'text-red-400'
                          }`}>
                            {indicators.trendSignal.toFixed(0)}%
                          </span>
                        </div>
                        <div className="w-full bg-slate-800 rounded-full h-1.5">
                          <div 
                            className={`h-1.5 rounded-full ${
                              indicators.trendSignal > 50 ? 'bg-green-400' : 
                              indicators.trendSignal > 25 ? 'bg-yellow-400' : 'bg-red-400'
                            }`}
                            style={{ width: `${Math.min(100, indicators.trendSignal)}%` }}
                          ></div>
                        </div>
                        <div className="text-xs text-slate-500 mt-1">核心策略 (39.2%)</div>
                      </div>

                      {/* 超短线策略信号 */}
                      <div className="bg-slate-700/50 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-slate-400 text-sm">⚡ 超短线策略</span>
                          <span className={`font-bold text-lg ${
                            indicators.scalpingSignal > 60 ? 'text-green-400' : 
                            indicators.scalpingSignal > 35 ? 'text-yellow-400' : 'text-red-400'
                          }`}>
                            {indicators.scalpingSignal.toFixed(0)}%
                          </span>
                        </div>
                        <div className="w-full bg-slate-800 rounded-full h-1.5">
                          <div 
                            className={`h-1.5 rounded-full ${
                              indicators.scalpingSignal > 60 ? 'bg-green-400' : 
                              indicators.scalpingSignal > 35 ? 'bg-yellow-400' : 'bg-red-400'
                            }`}
                            style={{ width: `${Math.min(100, indicators.scalpingSignal)}%` }}
                          ></div>
                        </div>
                        <div className="text-xs text-slate-500 mt-1">主力策略 (50.6%)</div>
                      </div>

                      {/* 超强趋势策略信号 */}
                      <div className="bg-slate-700/50 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-slate-400 text-sm">🚀 超强趋势</span>
                          <span className={`font-bold text-lg ${
                            indicators.superTrendSignal > 10 ? 'text-green-400' : 
                            indicators.superTrendSignal > 3 ? 'text-yellow-400' : 'text-red-400'
                          }`}>
                            {indicators.superTrendSignal.toFixed(0)}%
                          </span>
                        </div>
                        <div className="w-full bg-slate-800 rounded-full h-1.5">
                          <div 
                            className={`h-1.5 rounded-full ${
                              indicators.superTrendSignal > 10 ? 'bg-green-400' : 
                              indicators.superTrendSignal > 3 ? 'bg-yellow-400' : 'bg-red-400'
                            }`}
                            style={{ width: `${Math.min(100, indicators.superTrendSignal)}%` }}
                          ></div>
                        </div>
                        <div className="text-xs text-slate-500 mt-1">爆发利器 (0.7%)</div>
                      </div>
                    </div>

                    {/* 风险指标 */}
                    <div className="bg-slate-700/50 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <span className="text-slate-400">风险指标</span>
                        <span className="text-xs text-slate-400">实时评估</span>
                      </div>
                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div className="text-center">
                          <div className="text-slate-400 mb-1">波动率</div>
                          <div className="text-white font-bold">{(indicators.volatility * 100).toFixed(2)}%</div>
                        </div>
                        <div className="text-center">
                          <div className="text-slate-400 mb-1">回撤风险</div>
                          <div className={`font-bold ${
                            indicators.drawdownRisk < 15 ? 'text-green-400' : 
                            indicators.drawdownRisk < 25 ? 'text-yellow-400' : 'text-red-400'
                          }`}>
                            {indicators.drawdownRisk.toFixed(1)}%
                          </div>
                        </div>
                        <div className="text-center">
                          <div className="text-slate-400 mb-1">杠杆使用</div>
                          <div className="text-white font-bold">{indicators.leverageUsage.toFixed(2)}x</div>
                        </div>
                      </div>
                    </div>


                  </div>
                ) : (
                  <div className="text-center text-slate-400 py-8 flex-1 flex flex-col justify-center">
                    <div className="mb-3">🎯</div>
                    <p>{isMonitoring ? '计算信号强度中...' : '请开启监控以查看策略信号'}</p>
                    <p className="text-sm text-slate-500 mt-1">
                      {isMonitoring ? '需要足够价格数据' : '监控未启动'}
                    </p>
                  </div>
                )}
              </div>
            </div>



          </div>

          {/* 策略终极版状态监控 */}
          <div className="bg-gradient-to-r from-purple-900/30 to-blue-900/30 backdrop-blur-sm rounded-xl border border-purple-500/30 p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-white flex items-center gap-2">
                ⚡ 策略终极版状态
                {strategyStatus?.isRunning && (
                  <span className="text-xs bg-green-500/20 text-green-400 px-2 py-1 rounded animate-pulse">
                    运行中
                  </span>
                )}
              </h2>
              <div className="flex items-center gap-3">
                <div className={`w-3 h-3 rounded-full ${
                  strategyStatus?.isRunning ? 'bg-green-400 animate-pulse' : 'bg-gray-400'
                }`}></div>
                <div className="text-sm text-slate-400">
                  {strategyStatus?.isRunning ? '多策略执行中' : '策略未运行'}
                </div>
              </div>
            </div>

            {(() => {
              // 获取详细的调试信息
              const isStrategyRunning = strategyExecutor.isStrategyRunning()
              const executorStatus = isStrategyRunning ? strategyExecutor.getStrategyStatus() : null
              const currentStrategyStatus = strategyStatus || executorStatus
              
              console.log('🔍 策略状态详细检查:', {
                tradingStatus,
                selectedSymbol,
                isMonitoring,
                isStrategyRunning,
                strategyStatus,
                executorStatus,
                currentStrategyStatus,
                localStorage_trading_status: localStorage.getItem('trading_status'),
                localStorage_selected_symbol: localStorage.getItem('selected_symbol')
              })
              
              if (!currentStrategyStatus || !currentStrategyStatus.isRunning) {
                return (
                  <div className="text-center text-slate-400 py-12">
                    <div className="text-4xl mb-4">⚡</div>
                    <div className="text-lg mb-2">策略终极版未运行</div>
                    <div className="text-sm text-slate-500 mb-4">
                      请前往"策略配置"页面点击"开始交易"启动策略终极版
                    </div>
                    <div className="text-xs text-slate-600 mb-4 font-mono">
                      调试: trading_status={tradingStatus}, isStrategyRunning={isStrategyRunning ? 'true' : 'false'}
                    </div>
                    <div className="flex gap-3">
                      <button
                        onClick={async () => {
                          if (selectedSymbol && selectedSymbol !== '未设置') {
                            console.log('🚀 手动启动策略执行器...')
                            try {
                              const result = await strategyExecutor.startStrategy(selectedSymbol)
                              if (result) {
                                setTimeout(() => {
                                  const status = strategyExecutor.getStrategyStatus()
                                  setStrategyStatus(status)
                                  console.log('✅ 手动启动成功，策略状态已更新:', status)
                                }, 1000)
                              }
                            } catch (error) {
                              console.error('❌ 手动启动失败:', error)
                            }
                          }
                        }}
                        className="px-4 py-2 bg-green-600/30 hover:bg-green-600/40 border border-green-600/50 rounded text-green-400 transition-colors"
                      >
                        🚀 手动启动策略
                      </button>
                      <button
                        onClick={() => {
                          console.log('🔧 策略调试信息:')
                          console.log('- 策略执行器状态:', strategyExecutor.isStrategyRunning())
                          console.log('- 交易模式:', strategyExecutor.getTradingMode())
                          console.log('- 策略状态:', strategyExecutor.getStrategyStatus())
                          console.log('- 账户服务状态:', accountService.getConnectionStatus())
                          console.log('- 账户信息:', accountService.getCachedAccountInfo())
                          console.log('- 价格服务状态:', globalPriceService.isConnected())
                          console.log('- 当前价格:', globalPriceService.getCurrentPrice())
                        }}
                        className="px-4 py-2 bg-blue-600/30 hover:bg-blue-600/40 border border-blue-600/50 rounded text-blue-400 transition-colors"
                      >
                        🔧 调试策略状态
                      </button>
                      <button
                        onClick={() => window.location.href = '#/strategy'}
                        className="px-4 py-2 bg-purple-600/30 hover:bg-purple-600/40 border border-purple-600/50 rounded text-purple-400 transition-colors"
                      >
                        ⚙️ 前往策略配置
                      </button>
                    </div>
                  </div>
                )
              }
              
              return (
                <div className="space-y-4">
                  {/* 策略执行概览 */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                    <div className="bg-purple-500/10 border border-purple-500/30 rounded-lg p-4 text-center">
                      <div className="text-purple-300 text-sm mb-1">策略交易次数</div>
                      <div className="text-white font-bold text-lg">{currentStrategyStatus.totalTrades}</div>
                      <div className="text-purple-400 text-xs">累积执行</div>
                    </div>
                    <div className="bg-green-500/10 border border-green-500/30 rounded-lg p-4 text-center">
                      <div className="text-green-300 text-sm mb-1">胜率</div>
                      <div className="text-green-400 font-bold text-lg">{currentStrategyStatus.winRate.toFixed(1)}%</div>
                      <div className="text-green-400 text-xs">盈利比例</div>
                    </div>
                    <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-4 text-center">
                      <div className="text-blue-300 text-sm mb-1">当前杠杆</div>
                      <div className="text-blue-400 font-bold text-lg">{currentStrategyStatus.leverage.toFixed(2)}x</div>
                      <div className="text-blue-400 text-xs">动态调整</div>
                    </div>
                    <div className="bg-orange-500/10 border border-orange-500/30 rounded-lg p-4 text-center">
                      <div className="text-orange-300 text-sm mb-1">风险等级</div>
                      <div className={`font-bold text-lg ${
                        currentStrategyStatus.riskLevel === 'LOW' ? 'text-green-400' :
                        currentStrategyStatus.riskLevel === 'MEDIUM' ? 'text-yellow-400' :
                        currentStrategyStatus.riskLevel === 'HIGH' ? 'text-orange-400' : 'text-red-400'
                      }`}>
                        {currentStrategyStatus.riskLevel === 'LOW' ? '低' :
                         currentStrategyStatus.riskLevel === 'MEDIUM' ? '中' :
                         currentStrategyStatus.riskLevel === 'HIGH' ? '高' : '极高'}
                      </div>
                      <div className="text-orange-400 text-xs">实时评估</div>
                    </div>
                </div>

                {/* 策略详细状态 */}
                <div className="bg-slate-700/30 rounded-lg p-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-3">
                      <h3 className="text-lg font-semibold text-white">策略执行状态</h3>
                      <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-slate-400">市场状态:</span>
                            <span className="text-purple-400 font-medium">{currentStrategyStatus.marketState}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-slate-400">当前持仓:</span>
                            <span className="text-white">${currentStrategyStatus.currentPosition.toFixed(2)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-slate-400">已实现盈亏:</span>
                            <span className={`font-medium ${
                              currentStrategyStatus.realizedPnl >= 0 ? 'text-green-400' : 'text-red-400'
                            }`}>
                              {currentStrategyStatus.realizedPnl >= 0 ? '+' : ''}${currentStrategyStatus.realizedPnl.toFixed(2)}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-slate-400">未实现盈亏:</span>
                            <span className={`font-medium ${
                              currentStrategyStatus.unrealizedPnl >= 0 ? 'text-green-400' : 'text-red-400'
                            }`}>
                              {currentStrategyStatus.unrealizedPnl >= 0 ? '+' : ''}${currentStrategyStatus.unrealizedPnl.toFixed(2)}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-slate-400">最大回撤:</span>
                            <span className="text-orange-400">{(currentStrategyStatus.maxDrawdown * 100).toFixed(2)}%</span>
                          </div>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <h3 className="text-lg font-semibold text-white">策略性能指标</h3>
                      <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-slate-400">盈利因子:</span>
                            <span className="text-green-400 font-medium">{currentStrategyStatus.profitFactor.toFixed(2)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-slate-400">最后信号时间:</span>
                            <span className="text-white">
                              {currentStrategyStatus.lastSignalTime > 0 ? 
                                new Date(currentStrategyStatus.lastSignalTime).toLocaleTimeString() : '无'}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-slate-400">下次重平衡:</span>
                            <span className="text-blue-400">
                              {new Date(currentStrategyStatus.nextRebalanceTime).toLocaleTimeString()}
                            </span>
                          </div>
                        <div className="flex justify-between">
                          <span className="text-slate-400">策略权重:</span>
                          <span className="text-purple-400">动态调整</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-slate-400">执行频率:</span>
                          <span className="text-blue-400">超高频</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 策略提示 */}
                  <div className="mt-4 p-3 bg-purple-500/10 border border-purple-500/30 rounded-lg">
                    <div className="text-purple-300 text-sm font-medium mb-1">🎯 策略终极版运行状态</div>
                    <div className="text-purple-200 text-xs">
                      多策略融合系统正在运行，包含超短线(50.6%)、趋势(39.2%)、网格(9.5%)、超强趋势(0.7%)四大策略模块，
                      基于实时市场状态动态切换和协调执行，目标年化收益450.79%。
                    </div>
                  </div>
                  </div>
                </div>
              )
            })()}
          </div>

          {/* 账户持仓 - 新增模块 */}
          <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700/50 p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-white flex items-center gap-2">
                💼 账户持仓
                {isConnected && isMonitoring && (
                  <span className="text-xs bg-green-500/20 text-green-400 px-2 py-1 rounded">
                    实时同步
                  </span>
                )}
              </h2>
              <div className="flex items-center gap-3">
                <button
                  onClick={handleRefreshAccount}
                  className="px-3 py-1 bg-blue-600/20 hover:bg-blue-600/30 border border-blue-600/30 rounded text-blue-400 text-sm transition-colors"
                >
                  🔄 手动重试
                </button>
                <div className="text-sm text-slate-400">
                  {(() => {
                    const status = getDetailedConnectionStatus()
                    if (!status.configStatus.isValid) return '❌ 配置无效'
                    if (!status.hasValidCredentials) return '❌ 凭证无效'
                    if (!status.isMonitoring) return '⏸️ 监控未启动'
                    if (status.accountConnected && status.hasAccountInfo) return '✅ 实时更新'
                    return '🔄 连接中...'
                  })()}
                </div>
              </div>
            </div>

            {accountInfo && isMonitoring ? (
              <div className="space-y-4">
                {/* 账户概览 */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                  <div className="bg-slate-700/50 rounded-lg p-4 text-center">
                    <div className="text-slate-400 text-sm mb-1">账户权益</div>
                    <div className="text-white font-bold text-lg">${formatNumber(accountInfo.totalMarginBalance)}</div>
                    <div className={`text-xs ${
                      parseFloat(accountInfo.totalUnrealizedProfit) >= 0 ? 'text-green-400' : 'text-red-400'
                    }`}>
                      {parseFloat(accountInfo.totalUnrealizedProfit) >= 0 ? '+' : ''}
                      {((parseFloat(accountInfo.totalUnrealizedProfit) / parseFloat(accountInfo.totalWalletBalance)) * 100).toFixed(2)}%
                    </div>
                  </div>
                  <div className="bg-slate-700/50 rounded-lg p-4 text-center">
                    <div className="text-slate-400 text-sm mb-1">可用余额</div>
                    <div className="text-white font-bold text-lg">${formatNumber(accountInfo.availableBalance)}</div>
                    <div className="text-slate-400 text-xs">USDT</div>
                  </div>
                  <div className="bg-slate-700/50 rounded-lg p-4 text-center">
                    <div className="text-slate-400 text-sm mb-1">已用保证金</div>
                    <div className="text-yellow-400 font-bold text-lg">${formatNumber(accountInfo.totalPositionInitialMargin)}</div>
                    <div className="text-slate-400 text-xs">
                      {((parseFloat(accountInfo.totalPositionInitialMargin) / parseFloat(accountInfo.totalMarginBalance)) * 100).toFixed(1)}%
                    </div>
                  </div>
                  <div className="bg-slate-700/50 rounded-lg p-4 text-center">
                    <div className="text-slate-400 text-sm mb-1">未实现盈亏</div>
                    <div className={`font-bold text-lg ${
                      parseFloat(accountInfo.totalUnrealizedProfit) >= 0 ? 'text-green-400' : 'text-red-400'
                    }`}>
                      {parseFloat(accountInfo.totalUnrealizedProfit) >= 0 ? '+' : ''}${formatNumber(accountInfo.totalUnrealizedProfit)}
                    </div>
                    <div className={`text-xs ${
                      parseFloat(accountInfo.totalUnrealizedProfit) >= 0 ? 'text-green-400' : 'text-red-400'
                    }`}>
                      {parseFloat(accountInfo.totalUnrealizedProfit) >= 0 ? '+' : ''}
                      {((parseFloat(accountInfo.totalUnrealizedProfit) / parseFloat(accountInfo.totalWalletBalance)) * 100).toFixed(2)}%
                    </div>
                  </div>
                </div>

                {/* 持仓列表 */}
                <div className="bg-slate-700/30 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-white">当前持仓</h3>
                    <span className="text-xs text-slate-400 bg-slate-600/50 px-2 py-1 rounded">
                      {accountService.getActivePositions().length} 个活跃持仓
                    </span>
                  </div>

                  {/* 持仓表头 */}
                  <div className="grid grid-cols-7 gap-4 text-xs text-slate-400 mb-3 pb-2 border-b border-slate-600/30">
                    <div>合约</div>
                    <div>方向</div>
                    <div>数量</div>
                    <div>开仓价</div>
                    <div>标记价</div>
                    <div>未实现盈亏</div>
                    <div>杠杆</div>
                  </div>

                  {/* 持仓数据 */}
                  <div className="space-y-2">
                    {accountService.getActivePositions().length > 0 ? (
                      accountService.getActivePositions().map((position: Position, index: number) => {
                        const positionSide = getPositionSide(position.positionAmt)
                        const isLong = positionSide === 'LONG'
                        const pnlValue = parseFloat(position.unrealizedProfit || '0')
                        
                        return (
                          <div key={`${position.symbol}-${index}`} className="grid grid-cols-7 gap-4 text-sm py-2 bg-slate-800/30 rounded">
                            <div className="flex items-center gap-2">
                              <span className="text-orange-400">{getCryptoIcon(position.symbol)}</span>
                              <span className="text-white font-medium">{position.symbol}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <span className={`text-xs px-1.5 py-0.5 rounded ${
                                isLong ? 'text-green-400 bg-green-500/20' : 'text-red-400 bg-red-500/20'
                              }`}>
                                {isLong ? '多头' : '空头'}
                              </span>
                            </div>
                            <div className="text-white">
                              {Math.abs(parseFloat(position.positionAmt)).toFixed(3)} {position.symbol.replace('USDT', '')}
                            </div>
                            <div className="text-slate-300">${formatNumber(position.entryPrice)}</div>
                            <div className="text-white">
                              ${position.symbol === 'BTCUSDT' && priceData?.currentPrice 
                                ? formatNumber(priceData.currentPrice) 
                                : formatNumber(position.markPrice || '0')}
                            </div>
                            <div className={`font-medium ${pnlValue >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                              {pnlValue >= 0 ? '+' : ''}${formatNumber(position.unrealizedProfit || '0')}
                            </div>
                            <div className="text-slate-300">{position.leverage}x</div>
                          </div>
                        )
                      })
                    ) : (
                      <div className="text-center text-slate-400 py-8">
                        <div className="text-2xl mb-2">📊</div>
                        <div>暂无活跃持仓</div>
                        <div className="text-xs text-slate-500 mt-1">开仓后持仓将显示在这里</div>
                      </div>
                    )}
                  </div>

                  {/* 持仓统计 */}
                  <div className="mt-4 pt-3 border-t border-slate-600/30">
                    <div className="grid grid-cols-3 md:grid-cols-6 gap-4 text-xs">
                      <div className="text-center">
                        <div className="text-slate-400 mb-1">总持仓名义价值</div>
                        <div className="text-white font-semibold">
                          ${formatNumber(
                            accountService.getActivePositions().reduce((sum, pos) => 
                              sum + Math.abs(parseFloat(pos.notional || '0')), 0
                            )
                          )}
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-slate-400 mb-1">总保证金</div>
                        <div className="text-yellow-400 font-semibold">${formatNumber(accountInfo.totalPositionInitialMargin)}</div>
                      </div>
                      <div className="text-center">
                        <div className="text-slate-400 mb-1">维持保证金</div>
                        <div className="text-orange-400 font-semibold">${formatNumber(accountInfo.totalMaintMargin)}</div>
                      </div>
                      <div className="text-center">
                        <div className="text-slate-400 mb-1">平均杠杆</div>
                        <div className="text-white font-semibold">
                          {(() => {
                            const positions = accountService.getActivePositions()
                            if (positions.length === 0) return '0.00x'
                            const avgLeverage = positions.reduce((sum, pos) => sum + parseFloat(pos.leverage), 0) / positions.length
                            return `${avgLeverage.toFixed(2)}x`
                          })()}
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-slate-400 mb-1">保证金率</div>
                        <div className={`font-semibold ${
                          ((parseFloat(accountInfo.totalMaintMargin) / parseFloat(accountInfo.totalMarginBalance)) * 100) < 50 
                            ? 'text-green-400' : 'text-red-400'
                        }`}>
                          {((parseFloat(accountInfo.totalMaintMargin) / parseFloat(accountInfo.totalMarginBalance)) * 100).toFixed(1)}%
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-slate-400 mb-1">未实现盈亏</div>
                        <div className={`font-semibold ${
                          parseFloat(accountInfo.totalUnrealizedProfit) >= 0 ? 'text-green-400' : 'text-red-400'
                        }`}>
                          {parseFloat(accountInfo.totalUnrealizedProfit) >= 0 ? '+' : ''}${formatNumber(accountInfo.totalUnrealizedProfit)}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 风险提示 */}
                <div className={`border rounded-lg p-3 ${
                  ((parseFloat(accountInfo.totalMaintMargin) / parseFloat(accountInfo.totalMarginBalance)) * 100) > 80 
                    ? 'bg-red-500/10 border-red-500/30' 
                    : ((parseFloat(accountInfo.totalMaintMargin) / parseFloat(accountInfo.totalMarginBalance)) * 100) > 50
                    ? 'bg-yellow-500/10 border-yellow-500/30'
                    : 'bg-blue-500/10 border-blue-500/30'
                }`}>
                  <div className={`flex items-center gap-2 text-sm ${
                    ((parseFloat(accountInfo.totalMaintMargin) / parseFloat(accountInfo.totalMarginBalance)) * 100) > 80 
                      ? 'text-red-400' 
                      : ((parseFloat(accountInfo.totalMaintMargin) / parseFloat(accountInfo.totalMarginBalance)) * 100) > 50
                      ? 'text-yellow-400'
                      : 'text-blue-400'
                  }`}>
                    <span>
                      {((parseFloat(accountInfo.totalMaintMargin) / parseFloat(accountInfo.totalMarginBalance)) * 100) > 80 
                        ? '⚠️' 
                        : ((parseFloat(accountInfo.totalMaintMargin) / parseFloat(accountInfo.totalMarginBalance)) * 100) > 50
                        ? '🔶'
                        : '💡'}
                    </span>
                    <span>
                      <strong>风险提醒</strong>: 
                      {((parseFloat(accountInfo.totalMaintMargin) / parseFloat(accountInfo.totalMarginBalance)) * 100) > 80 
                        ? ' 保证金率过高，存在强平风险，建议立即减仓或追加保证金' 
                        : ((parseFloat(accountInfo.totalMaintMargin) / parseFloat(accountInfo.totalMarginBalance)) * 100) > 50
                        ? ' 保证金率偏高，建议谨慎操作，注意风险控制'
                        : ` 当前保证金率为 ${((parseFloat(accountInfo.totalMaintMargin) / parseFloat(accountInfo.totalMarginBalance)) * 100).toFixed(1)}%，风险可控，建议密切关注市场波动`}
                    </span>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center text-slate-400 py-12">
                <div className="text-4xl mb-4">💼</div>
                {(() => {
                  const status = getDetailedConnectionStatus()
                  
                  if (!status.configStatus.isValid) {
                    return (
                      <>
                        <div className="text-lg mb-2 text-orange-400">⚙️ 请配置API凭证</div>
                        <div className="text-sm text-slate-500 mb-4">
                          {status.configStatus.message}
                        </div>
                        <button
                          onClick={handleGoToSettings}
                          className="px-4 py-2 bg-orange-600/30 hover:bg-orange-600/40 border border-orange-600/50 rounded text-orange-400 transition-colors"
                        >
                          ⚙️ 前往系统设置
                        </button>
                      </>
                    )
                  }
                  
                  if (!status.hasValidCredentials) {
                    return (
                      <>
                        <div className="text-lg mb-2 text-red-400">❌ API凭证无效</div>
                        <div className="text-sm text-slate-500 mb-4">
                          系统配置中的API凭证无法加载，请检查配置
                        </div>
                        <div className="flex gap-3 justify-center">
                          <button
                            onClick={handleGoToSettings}
                            className="px-4 py-2 bg-orange-600/30 hover:bg-orange-600/40 border border-orange-600/50 rounded text-orange-400 transition-colors"
                          >
                            ⚙️ 检查设置
                          </button>
                          <button
                            onClick={handleRefreshAccount}
                            className="px-4 py-2 bg-blue-600/30 hover:bg-blue-600/40 border border-blue-600/50 rounded text-blue-400 transition-colors"
                          >
                            🔄 重试连接
                          </button>
                        </div>
                      </>
                    )
                  }
                  
                  if (!status.isMonitoring) {
                    return (
                      <>
                        <div className="text-lg mb-2">⏸️ 请启动监控以查看持仓</div>
                        <div className="text-sm text-slate-500 mb-4">监控未启动</div>
                        <button
                          onClick={handleStartTrading}
                          className="px-4 py-2 bg-blue-600/30 hover:bg-blue-600/40 border border-blue-600/50 rounded text-blue-400 transition-colors"
                        >
                          🚀 启动监控
                        </button>
                      </>
                    )
                  }
                  
                  // 监控已启动但无法获取账户信息
                  return (
                    <>
                      <div className="text-lg mb-2 text-yellow-400">🔄 连接账户中...</div>
                      <div className="text-sm text-slate-500 mb-4">
                        正在尝试连接币安账户，如果一直无法连接，请检查:
                        <br />• API Key和Secret是否正确
                        <br />• API是否有期货交易权限
                        <br />• 网络连接是否正常
                        {status.configStatus.useProxy && (
                          <><br />• 代理设置是否正确 ({status.configStatus.proxyHost}:{status.configStatus.proxyPort})</>
                        )}
                      </div>
                      <div className="flex gap-3 justify-center">
                        <button
                          onClick={handleRefreshAccount}
                          className="px-4 py-2 bg-blue-600/30 hover:bg-blue-600/40 border border-blue-600/50 rounded text-blue-400 transition-colors"
                        >
                          🔄 重试连接
                        </button>
                        <button
                          onClick={handleGoToSettings}
                          className="px-4 py-2 bg-slate-600/30 hover:bg-slate-600/40 border border-slate-600/50 rounded text-slate-400 transition-colors"
                        >
                          ⚙️ 检查设置
                        </button>
                      </div>
                    </>
                  )
                })()}
              </div>
            )}
          </div>

          {/* 性能监控概览卡片 */}
          <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700/50 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white flex items-center gap-2">
                📊 系统性能概览
              </h3>
              <button
                onClick={() => setActiveTab('performance')}
                className="px-3 py-1 bg-blue-600/20 hover:bg-blue-600/30 border border-blue-600/30 rounded text-blue-400 text-sm transition-colors"
              >
                查看详情
              </button>
            </div>
            <PerformanceMonitorDisplay 
              isExpanded={false} 
              onToggle={() => setActiveTab('performance')}
            />
          </div>

        </div>
      )}

      {/* 性能监控页面 */}
      {activeTab === 'performance' && (
        <div className="space-y-6">
          <PerformanceMonitorDisplay 
            isExpanded={performanceExpanded} 
            onToggle={() => setPerformanceExpanded(!performanceExpanded)}
          />
        </div>
      )}

      {/* 风险监控页面 */}
      {activeTab === 'risk' && <RiskMonitoring />}

      {/* 订单执行页面 */}
      {activeTab === 'orders' && <OrderExecution />}
    </div>
  )
}